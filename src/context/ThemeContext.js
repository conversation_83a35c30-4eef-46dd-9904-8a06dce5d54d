import {createContext,useState} from 'react';
export const themeContext = createContext(null);
export const ThemeContextProvider = ({children}) => {

    const [theme, setTheme] = useState("light");
    const [user, setUser] = useState({ name: "<PERSON><PERSON><PERSON>", role: "Instructor" });
    const toggleTheme = () => setTheme((t) => (t === "light" ? "dark" : "light"));

    return (
        <themeContext.Provider value={{ theme, user, toggleTheme }}>
        {children}
        </themeContext.Provider>
    )
}