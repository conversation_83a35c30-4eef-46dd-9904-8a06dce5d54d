import*as e from"../../core/common/common.js";import*as t from"../../core/platform/platform.js";import*as r from"../../core/sdk/sdk.js";function o(e){return e.startsWith("devtools://devtools/bundled/")}const s=Object.freeze({}),n=Promise.resolve();let i;class a extends e.ObjectWrapper.ObjectWrapper{#e;#t;#r=s;#o=n;get projectSettings(){return this.#r}get projectSettingsPromise(){return this.#o.then((()=>this.#r))}constructor(e,t,r){if(super(),this.#e=t,this.#t=r,e.devToolsWellKnown?.enabled){this.#t.addEventListener("InspectedURLChanged",this.#s,this);const e=this.#t.primaryPageTarget();null!==e&&this.#s({data:e})}}static instance({forceNew:e,hostConfig:t,pageResourceLoader:r,targetManager:o}){if(!i||e){if(!t||!r||!o)throw new Error("Unable to create ProjectSettingsModel: hostConfig, pageResourceLoader, and targetManager must be provided");i=new a(t,r,o)}return i}static removeInstance(){i&&(i.#n(),i=void 0)}#n(){this.#t.removeEventListener("InspectedURLChanged",this.#s,this)}#s(e){const t=e.data,r=this.#o=this.#o.then((async()=>{let e=s;try{e=await this.#i(t)}catch(e){console.debug(`Could not load project settings for ${t.inspectedURL()}: ${e.message}`)}this.#o===r&&(this.#r!==e&&(this.#r=e,this.dispatchEventToListeners("ProjectSettingsChanged",e)),this.#o=n)}))}async#i(e){const n=e.model(r.ResourceTreeModel.ResourceTreeModel)?.mainFrame;if(!function(e){return!!e&&(o(e.url)?"true"===new URL(e.url).searchParams.get("debugFrontend"):e.securityOriginDetails?.isLocalhost??!1)}(n))return s;const i=n.url,a=n.id;let c="/.well-known/appspecific/com.chrome.devtools.json";o(i)&&(c="/bundled"+c),c=new URL(c,i).toString();const{content:d}=await this.#e.loadResource(t.DevToolsPath.urlString`${c}`,{target:e,frameId:a,initiatorUrl:i}),g=JSON.parse(d);if(void 0!==g.workspace){const{workspace:e}=g;if("object"!=typeof e||null===e)throw new Error('Invalid "workspace" field');if("string"!=typeof e.root)throw new Error('Invalid or missing "workspace.root" field');if("string"!=typeof e.uuid)throw new Error('Invalid or missing "workspace.uuid" field')}return Object.freeze(g)}}var c=Object.freeze({__proto__:null,ProjectSettingsModel:a});export{c as ProjectSettingsModel};
