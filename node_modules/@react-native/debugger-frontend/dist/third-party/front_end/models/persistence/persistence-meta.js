import*as e from"../../core/common/common.js";import*as r from"../../core/i18n/i18n.js";import*as t from"../../core/sdk/sdk.js";import*as o from"../workspace/workspace.js";import*as s from"../../ui/legacy/legacy.js";const i={workspace:"Workspace",showWorkspace:"Show Workspace settings",enableLocalOverrides:"Enable Local Overrides",interception:"interception",override:"override",network:"network",rewrite:"rewrite",request:"request",enableOverrideNetworkRequests:"Enable override network requests",disableOverrideNetworkRequests:"Disable override network requests",enableAutomaticWorkspaceFolders:"Enable automatic workspace folders"},a=r.i18n.registerUIStrings("models/persistence/persistence-meta.ts",i),n=r.i18n.getLazilyComputedLocalizedString.bind(void 0,a);let c;async function l(){return c||(c=await import("./persistence.js")),c}s.ViewManager.registerViewExtension({location:"settings-view",id:"workspace",title:n(i.workspace),commandPrompt:n(i.showWorkspace),order:1,loadView:async()=>new((await l()).WorkspaceSettingsTab.WorkspaceSettingsTab),iconName:"folder"}),e.Settings.registerSettingExtension({category:"PERSISTENCE",title:n(i.enableAutomaticWorkspaceFolders),settingName:"persistence-automatic-workspace-folders",settingType:"boolean",defaultValue:!1}),e.Settings.registerSettingExtension({category:"PERSISTENCE",title:n(i.enableLocalOverrides),settingName:"persistence-network-overrides-enabled",settingType:"boolean",defaultValue:!1,tags:[n(i.interception),n(i.override),n(i.network),n(i.rewrite),n(i.request)],options:[{value:!0,title:n(i.enableOverrideNetworkRequests)},{value:!1,title:n(i.disableOverrideNetworkRequests)}]}),s.ContextMenu.registerProvider({contextTypes:()=>[o.UISourceCode.UISourceCode,t.Resource.Resource,t.NetworkRequest.NetworkRequest],loadProvider:async()=>new((await l()).PersistenceActions.ContextMenuProvider),experiment:void 0});
