import*as e from"../../../third_party/third-party-web/third-party-web.js";import*as t from"../types/types.js";import*as n from"../helpers/helpers.js";import*as i from"../../../core/platform/platform.js";import*as s from"../../../core/root/root.js";import*as a from"../../cpu_profile/cpu_profile.js";import*as r from"../../../core/common/common.js";function o(e,t){const n=l(e);if(n)return c(n,t)}function c(t,n){return e.ThirdPartyWeb.getEntity(t)??d(n,t)}function l(e,n){if(t.Events.isProfileCall(e))return e.callFrame.url;if(t.Events.isSyntheticNetworkRequest(e))return e.args.data.url;if(e.args?.data?.stackTrace&&e.args.data.stackTrace.length>0)return e.args.data.stackTrace[0].url;if(t.Events.isParseHTML(e))return e.args.beginData.url;if(n){if(t.Events.isDecodeImage(e)){const t=n.ImagePainting.paintImageForEvent.get(e);return t?l(t,n):null}if(t.Events.isDrawLazyPixelRef(e)&&e.args?.LazyPixelRef){const t=n.ImagePainting.paintImageByDrawLazyPixelRef.get(e.args.LazyPixelRef);return t?l(t,n):null}}return e.args?.data?.url?e.args.data.url:null}function d(t,n){if(n.startsWith("chrome-extension:"))return function(e,t,n){const i=new URL(t),s=function(e){return e.protocol+"//"+e.host}(i),a=new URL(s).host,r=n||a,o=e.get(s);if(o)return o;const c={name:r,company:r,category:"Chrome Extension",homepage:"https://chromewebstore.google.com/detail/"+a,categories:[],domains:[s],averageExecutionTime:0,totalExecutionTime:0,totalOccurrences:0};return e.set(s,c),c}(t,n);if(!n.startsWith("http"))return;const i=e.ThirdPartyWeb.getRootDomain(n);if(!i)return;if(t.has(i))return t.get(i);const s={name:i,company:i,category:"",categories:[],domains:[i],averageExecutionTime:0,totalExecutionTime:0,totalOccurrences:0,isUnrecognized:!0};return t.set(i,s),s}function u(e,t){const n=o(e,t.createdEntityCache);if(!n)return;if(t.entityByEvent.has(e))return;const i=t.eventsByEntity.get(n);i?i.push(e):t.eventsByEntity.set(n,[e]),t.entityByEvent.set(e,n)}function f(e,t,n){const i=o(e,t.createdEntityCache);if(!i)return;const s=[e,...Object.values(n).flat()],a=t.eventsByEntity.get(i);a?a.push(...s):t.eventsByEntity.set(i,s);for(const e of s)t.entityByEvent.set(e,i)}var m=Object.freeze({__proto__:null,addEventToEntityMapping:u,addNetworkRequestToEntityMapping:f,getEntityForEvent:o,getEntityForUrl:c,getNonResolvedURL:l,makeUpEntity:d});function g(e){return`${e.pid}-${e.tid}`}const p=new Map,h=new Map,v=new Map,E=[],T=new Map;var y=Object.freeze({__proto__:null,data:function(){return{animationFrames:E,presentationForFrame:T}},deps:function(){return["Meta"]},finalize:async function(){for(const[e,i]of p.entries()){const s=h.get(e);if(s){n.Trace.sortTraceEventsInPlace(i),n.Trace.sortTraceEventsInPlace(s);for(let e=0;e<i.length;e++){const a=s.at(e);if(!a)break;const r=i[e],o=n.SyntheticEvents.SyntheticEventsManager.registerSyntheticEvent({rawSourceEvent:r,...r,dur:t.Timing.Micro(a.ts-r.ts),args:{data:{beginEvent:r,endEvent:a}}});E.push(o);const c=r.args?.id;if(c){const e=v.get(c);e&&T.set(o,e)}}}}},handleEvent:function(e){if(t.Events.isAnimationFrameAsyncStart(e)){const t=g(e),n=p.get(t)??[];n.push(e),p.set(t,n)}else if(t.Events.isAnimationFrameAsyncEnd(e)){const t=g(e),n=h.get(t)??[];n.push(e),h.set(t,n)}else t.Events.isAnimationFramePresentation(e)&&e.args?.id&&v.set(e.args.id,e)},reset:function(){p.clear(),h.clear(),E.length=0,T.clear(),v.clear()}});const M=[],I=[];var w=Object.freeze({__proto__:null,data:function(){return{animations:I}},finalize:async function(){const e=n.Trace.createMatchedSortedSyntheticEvents(M);I.push(...e)},handleEvent:function(e){t.Events.isAnimation(e)&&M.push(e)},reset:function(){M.length=0,I.length=0}});const S=new Map,F=new Map,P=new Map,_=[],R=[];let C=[];const L="-$-";function k(e){const t=F.get(e.ts)?.get(e.pid)?.get(e.tid)?.get(e.cat);if(!t)return;const{flows:n,bindingParsed:s}=t;if(!s){for(const t of n){i.MapUtilities.getWithDefault(P,t,(()=>new Map)).set(e.ts,e)}t.bindingParsed=!0}}function b(e){const t=`${(n=e).cat}${L}${n.name}${L}${n.id}`;var n;switch(e.ph){case"s":{const n={flowId:e.id,times:new Map([[e.ts,void 0]])};return S.set(t,e.id),void D(e,n.flowId)}case"t":{const n=S.get(t);if(void 0===n)return;return void D(e,n)}case"f":{const n=S.get(t);if(void 0===n)return;D(e,n),S.delete(t)}}}function D(e,t){const n=i.MapUtilities.getWithDefault(F,e.ts,(()=>new Map)),s=i.MapUtilities.getWithDefault(n,e.pid,(()=>new Map)),a=i.MapUtilities.getWithDefault(s,e.tid,(()=>new Map));i.MapUtilities.getWithDefault(a,e.cat,(()=>({flows:new Set,bindingParsed:!1}))).flows.add(t)}function N(){return{flows:C}}var B=Object.freeze({__proto__:null,data:N,finalize:async function(){_.forEach(b),R.forEach(k),C=[...P.values()].map((e=>[...e.values()])).map((e=>e.filter((e=>void 0!==e)))).filter((e=>e.length>1))},handleEvent:function(e){t.Events.isFlowPhaseEvent(e)?_.push(e):R.push(e)},reset:function(){C=[],_.length=0,R.length=0,S.clear(),F.clear(),P.clear()}});const W=new Map,U=new Map,O=new Map,x=new Map,z=new Map;function A(e){switch(e){case"seller":return"seller";case"bidder":return"bidder";default:return"unknown"}}function q(e){return n.SyntheticEvents.SyntheticEventsManager.registerSyntheticEvent({rawSourceEvent:e,name:"SyntheticAuctionWorklet",s:"t",cat:e.cat,tid:e.tid,ts:e.ts,ph:"I",pid:e.args.data.pid,host:e.args.data.host,target:e.args.data.target,type:A(e.args.data.type)})}function j(){return{worklets:new Map(O)}}var H=Object.freeze({__proto__:null,data:j,finalize:async function(){for(const[e,t]of x){const n=z.get(e);if(!n)continue;const i=W.get(e),s=U.get(e);let a=null;i?(a={...q(i),args:{data:{runningInProcessEvent:i,utilityThread:t,v8HelperThread:n}}},s&&(a.args.data.doneWithProcessEvent=s)):s&&(a={...q(s),args:{data:{doneWithProcessEvent:s,utilityThread:t,v8HelperThread:n}}},i&&(a.args.data.runningInProcessEvent=i)),null!==a&&O.set(e,a)}},handleEvent:function(e){if(t.Events.isAuctionWorkletRunningInProcess(e))W.set(e.args.data.pid,e);else if(t.Events.isAuctionWorkletDoneWithProcess(e))U.set(e.args.data.pid,e);else if(t.Events.isThreadName(e)){if("auction_worklet.CrUtilityMain"===e.args.name)return void x.set(e.pid,e);"AuctionV8HelperThread"===e.args.name&&z.set(e.pid,e)}},reset:function(){W.clear(),U.clear(),O.clear(),x.clear(),z.clear()}});const $=new Map;let G="",V="";const X=new Map;let Y=t.Events.ProcessID(-1),K=t.Events.ThreadID(-1),Q=t.Events.ProcessID(-1),J=t.Events.ThreadID(-1),Z=null,ee=null;const te=new Map,ne=new Set,ie={min:t.Timing.Micro(Number.POSITIVE_INFINITY),max:t.Timing.Micro(Number.NEGATIVE_INFINITY),range:t.Timing.Micro(Number.POSITIVE_INFINITY)},se=new Map,ae=new Map,re=new Map,oe=[],ce=new Map;let le=t.Timing.Micro(-1);const de=new Set(["B","E","X","I"]);let ue=!0;const fe=new Set(["TracingStartedInPage","TracingSessionIdForWorker","TracingStartedInBrowser"]);function me(e,n){i.MapUtilities.getWithDefault(X,n.processId,(()=>new Map)).set(n.frame,n);const s=i.MapUtilities.getWithDefault($,n.frame,(()=>new Map)),a=i.MapUtilities.getWithDefault(s,n.processId,(()=>[])),r=a.at(-1);r&&r.frame.url===n.url||a.push({frame:n,window:{min:e.ts,max:t.Timing.Micro(0),range:t.Timing.Micro(0)}})}function ge(){return{traceBounds:{...ie},browserProcessId:Y,browserThreadId:K,processNames:te,gpuProcessId:Q,gpuThreadId:J===t.Events.ThreadID(-1)?void 0:J,viewportRect:Z||void 0,devicePixelRatio:ee??void 0,mainFrameId:G,mainFrameURL:V,navigationsByFrameId:se,navigationsByNavigationId:ae,finalDisplayUrlByNavigationId:re,threadsInProcess:ce,rendererProcessesByFrame:$,topLevelRendererIds:ne,frameByProcessId:X,mainFrameNavigations:oe,traceIsGeneric:ue}}var pe=Object.freeze({__proto__:null,data:ge,finalize:async function(){le>=0&&(ie.min=le),ie.range=t.Timing.Micro(ie.max-ie.min);for(const[,e]of $){const n=[...e.values()].flat().sort(((e,t)=>e.window.min-t.window.min));for(let e=0;e<n.length;e++){const i=n[e],s=n[e+1];s?(i.window.max=t.Timing.Micro(s.window.min-1),i.window.range=t.Timing.Micro(i.window.max-i.window.min)):(i.window.max=t.Timing.Micro(ie.max),i.window.range=t.Timing.Micro(ie.max-i.window.min))}}for(const[e,t]of se)if(!$.has(e)){se.delete(e);for(const e of t)e.args.data&&ae.delete(e.args.data.navigationId)}const e=oe.at(0),i=n.Timing.secondsToMicro(t.Timing.Seconds(.5));if(e){const t=e.ts-ie.min<i;e.args.data?.isOutermostMainFrame&&e.args.data?.documentLoaderURL&&t&&(V=e.args.data.documentLoaderURL)}},handleEvent:function(e){if(ue&&fe.has(e.name)&&(ue=!1),t.Events.isProcessName(e)&&te.set(e.pid,e),0!==e.ts&&!e.name.endsWith("::UMA")&&de.has(e.ph)){ie.min=t.Timing.Micro(Math.min(e.ts,ie.min));const n=e.dur??t.Timing.Micro(0);ie.max=t.Timing.Micro(Math.max(e.ts+n,ie.max))}if(!t.Events.isProcessName(e)||"Browser"!==e.args.name&&"HeadlessBrowser"!==e.args.name)if(!t.Events.isProcessName(e)||"Gpu"!==e.args.name&&"GPU Process"!==e.args.name)if(t.Events.isThreadName(e)&&"CrGpuMain"===e.args.name)J=e.tid;else{if(t.Events.isThreadName(e)&&"CrBrowserMain"===e.args.name&&(K=e.tid),t.Events.isMainFrameViewport(e)&&null===Z){const t=e.args.data.viewport_rect,n=t[0],i=t[1],s=t[2],a=t[5];Z=new DOMRect(n,i,s,a),ee=e.args.data.dpr}if(t.Events.isTracingStartedInBrowser(e)){if(le=e.ts,!e.args.data)throw new Error("No frames found in trace data");for(const t of e.args.data.frames??[]){me(e,t),t.parent||ne.add(t.processId);const n="isOutermostMainFrame"in t;"isInPrimaryMainFrame"in t&&n?t.isInPrimaryMainFrame&&t.isOutermostMainFrame&&(G=t.frame,V=t.url):n?t.isOutermostMainFrame&&(G=t.frame,V=t.url):!t.parent&&t.url&&(G=t.frame,V=t.url)}}else if(t.Events.isFrameCommittedInBrowser(e)){const t=e.args.data;if(!t)return;if(me(e,t),t.parent)return;ne.add(t.processId)}else if(t.Events.isCommitLoad(e)){const t=e.args.data;if(!t)return;const{frame:n,name:i,url:s}=t;me(e,{processId:e.pid,frame:n,name:i,url:s})}else if(t.Events.isThreadName(e)){i.MapUtilities.getWithDefault(ce,e.pid,(()=>new Map)).set(e.tid,e)}else{if(t.Events.isNavigationStart(e)&&e.args.data){const t=e.args.data.navigationId;if(ae.has(t))return;ae.set(t,e),re.set(t,e.args.data.documentLoaderURL);const n=e.args.frame,i=se.get(n)||[];return i.push(e),se.set(n,i),void(n===G&&oe.push(e))}if(t.Events.isResourceSendRequest(e)){if("Document"!==e.args.data.resourceType)return;const t=e.args.data.requestId;if(!ae.get(t))return;re.set(t,e.args.data.url)}else if(t.Events.isDidCommitSameDocumentNavigation(e)){if("PRIMARY_MAIN_FRAME"!==e.args.render_frame_host.frame_type)return;const t=oe.at(-1),n=t?.args.data?.navigationId??"";re.set(n,e.args.url)}else;}}else Q=e.pid;else Y=e.pid},reset:function(){se.clear(),ae.clear(),re.clear(),te.clear(),oe.length=0,Y=t.Events.ProcessID(-1),K=t.Events.ThreadID(-1),Q=t.Events.ProcessID(-1),J=t.Events.ThreadID(-1),Z=null,ne.clear(),ce.clear(),$.clear(),X.clear(),ie.min=t.Timing.Micro(Number.POSITIVE_INFINITY),ie.max=t.Timing.Micro(Number.NEGATIVE_INFINITY),ie.range=t.Timing.Micro(Number.POSITIVE_INFINITY),le=t.Timing.Micro(-1),ue=!0}});const he=1e3,ve=1e6,Ee=new Map,Te=new Map,ye=new Map,Me=new Map,Ie=[],we=new Map,Se=new Map,Fe={eventsByEntity:new Map,entityByEvent:new Map,createdEntityCache:new Map};function Pe(e,t,n){Te.has(e)||Te.set(e,{});const i=Te.get(e);if(!i)throw new Error(`Unable to locate trace events for request ID ${e}`);if(Array.isArray(i[t])){const e=n;i[t].push(...e)}else i[t]=n}function _e(e){for(const t of e)if(t>0)return t;return 0}function Re(){return{byId:ye,byOrigin:Me,byTime:Ie,eventToInitiator:Se,webSocket:[...Ee.values()],entityMappings:{entityByEvent:new Map(Fe.entityByEvent),eventsByEntity:new Map(Fe.eventsByEntity),createdEntityCache:new Map(Fe.createdEntityCache)}}}var Ce=Object.freeze({__proto__:null,data:Re,deps:function(){return["Meta"]},finalize:async function(){const{rendererProcessesByFrame:e}=ge();for(const[s,a]of Te.entries()){if(!a.sendRequests||!a.receiveResponse)continue;const r=[];for(let e=0;e<a.sendRequests.length-1;e++){const n=a.sendRequests[e],i=a.sendRequests[e+1];let s=n.ts,o=t.Timing.Micro(i.ts-n.ts);if(a.willSendRequests?.[e]&&a.willSendRequests[e+1]){const n=a.willSendRequests[e],i=a.willSendRequests[e+1];s=n.ts,o=t.Timing.Micro(i.ts-n.ts)}r.push({url:n.args.data.url,priority:n.args.data.priority,requestMethod:n.args.data.requestMethod,ts:s,dur:o})}const o=0!==a.resourceFinish?.args.data.encodedDataLength,c=a.receiveResponse.args.data.fromCache&&!a.receiveResponse.args.data.fromServiceWorker&&!o,l=void 0!==a.resourceMarkAsCached,d=l?void 0:a.receiveResponse.args.data.timing;if(!d&&!l)continue;const u=a.sendRequests[0],m=a.sendRequests[a.sendRequests.length-1],g=m.args.data.priority;let p=g;a.changePriority&&(p=a.changePriority.args.data.priority);const h=a.willSendRequests?.length?t.Timing.Micro(a.willSendRequests[0].ts):t.Timing.Micro(u.ts),v=a.willSendRequests?.length?t.Timing.Micro(a.willSendRequests[a.willSendRequests.length-1].ts):t.Timing.Micro(m.ts),E=a.resourceFinish?a.resourceFinish.ts:v,T=a.resourceFinish?.args.data.finishTime?t.Timing.Micro(a.resourceFinish.args.data.finishTime*ve):t.Timing.Micro(E),y=t.Timing.Micro(d?(T||v)-v:0),M=t.Timing.Micro(E-(T||E)),I=t.Timing.Micro(v-h),w=d?d.requestTime*ve-v:0,S=t.Timing.Micro(i.NumberUtilities.clamp(w,0,Number.MAX_VALUE)),F=d?t.Timing.Micro(_e([d.dnsStart*he,d.connectStart*he,d.sendStart*he,a.receiveResponse.ts-v])):t.Timing.Micro(a.receiveResponse.ts-h),P=d?t.Timing.Micro(d.requestTime*ve+d.sendStart*he):h,_=d?t.Timing.Micro((d.receiveHeadersEnd-d.sendEnd)*he):t.Timing.Micro(0),R=d?t.Timing.Micro(d.requestTime*ve+d.receiveHeadersEnd*he):h,C=d?t.Timing.Micro((T||R)-R):t.Timing.Micro(E-a.receiveResponse.ts),L=t.Timing.Micro(y+M),k=d?t.Timing.Micro((d.dnsEnd-d.dnsStart)*he):t.Timing.Micro(0),b=d?t.Timing.Micro((d.sslEnd-d.sslStart)*he):t.Timing.Micro(0),D=d?t.Timing.Micro((d.proxyEnd-d.proxyStart)*he):t.Timing.Micro(0),N=d?t.Timing.Micro((d.sendEnd-d.sendStart)*he):t.Timing.Micro(0),B=d?t.Timing.Micro((d.connectEnd-d.connectStart)*he):t.Timing.Micro(0),{frame:W,url:U,renderBlocking:O}=m.args.data,{encodedDataLength:x,decodedBodyLength:z}=a.resourceFinish?a.resourceFinish.args.data:{encodedDataLength:0,decodedBodyLength:0},A=new URL(U),q="https:"===A.protocol,j=n.Trace.activeURLForFrameAtTime(W,m.ts,e)||"",H=n.SyntheticEvents.SyntheticEventsManager.registerSyntheticEvent({rawSourceEvent:m,args:{data:{syntheticData:{dnsLookup:k,download:C,downloadStart:R,finishTime:T,initialConnection:B,isDiskCached:c,isHttps:q,isMemoryCached:l,isPushedResource:o,networkDuration:y,processingDuration:M,proxyNegotiation:D,queueing:S,redirectionDuration:I,requestSent:N,sendStartTime:P,ssl:b,stalled:F,totalTime:L,waiting:_},decodedBodyLength:z,encodedDataLength:x,frame:W,fromServiceWorker:a.receiveResponse.args.data.fromServiceWorker,isLinkPreload:m.args.data.isLinkPreload||!1,mimeType:a.receiveResponse.args.data.mimeType,priority:p,initialPriority:g,protocol:a.receiveResponse.args.data.protocol??"unknown",redirects:r,renderBlocking:O??"non_blocking",requestId:s,requestingFrameUrl:j,requestMethod:m.args.data.requestMethod,resourceType:m.args.data.resourceType??"Other",statusCode:a.receiveResponse.args.data.statusCode,responseHeaders:a.receiveResponse.args.data.headers||[],fetchPriorityHint:m.args.data.fetchPriorityHint??"auto",initiator:m.args.data.initiator,stackTrace:m.args.data.stackTrace,timing:d,url:U,failed:a.resourceFinish?.args.data.didFail??!1,finished:Boolean(a.resourceFinish),connectionId:a.receiveResponse.args.data.connectionId,connectionReused:a.receiveResponse.args.data.connectionReused}},cat:"loading",name:"SyntheticNetworkRequest",ph:"X",dur:t.Timing.Micro(E-h),tdur:t.Timing.Micro(E-h),ts:t.Timing.Micro(h),tts:t.Timing.Micro(h),pid:m.pid,tid:m.tid}),$=i.MapUtilities.getWithDefault(Me,A.host,(()=>({renderBlocking:[],nonRenderBlocking:[],all:[]})));n.Network.isSyntheticNetworkRequestEventRenderBlocking(H)?$.renderBlocking.push(H):$.nonRenderBlocking.push(H),$.all.push(H),Ie.push(H),ye.set(H.args.data.requestId,H),f(H,Fe,a);const G=H.args.data.initiator?.url||n.Trace.getZeroIndexedStackTraceForEvent(H)?.at(0)?.url;if(G){const e=we.get(G)??[];e.push(H),we.set(G,e)}}for(const e of Ie){const t=we.get(e.args.data.url);if(t)for(const n of t)Se.set(n,e)}Ee.forEach((e=>{let n=null,i=null;for(const s of e.events)t.Events.isWebSocketCreate(s)&&(n=s),t.Events.isWebSocketDestroy(s)&&(i=s);e.syntheticConnection=function(e,t,n){const{traceBounds:i}=ge(),s=e?e.ts:i.min,a=t?t.ts:i.max,r=a-s,o=e||t||n;return{name:"SyntheticWebSocketConnection",cat:o.cat,ph:"X",ts:s,dur:r,pid:o.pid,tid:o.tid,s:o.s,rawSourceEvent:o,_tag:"SyntheticEntryTag",args:{data:{identifier:o.args.data.identifier,priority:"Low",url:o.args.data.url||""}}}}(n,i,e.events[0])}))},handleEvent:function(e){if(t.Events.isResourceChangePriority(e))Pe(e.args.data.requestId,"changePriority",e);else if(t.Events.isResourceWillSendRequest(e))Pe(e.args.data.requestId,"willSendRequests",[e]);else if(t.Events.isResourceSendRequest(e))Pe(e.args.data.requestId,"sendRequests",[e]);else if(t.Events.isResourceReceiveResponse(e))Pe(e.args.data.requestId,"receiveResponse",e);else if(t.Events.isResourceReceivedData(e))Pe(e.args.data.requestId,"receivedData",[e]);else if(t.Events.isResourceFinish(e))Pe(e.args.data.requestId,"resourceFinish",e);else if(t.Events.isResourceMarkAsCached(e))Pe(e.args.data.requestId,"resourceMarkAsCached",e);else if(t.Events.isWebSocketCreate(e)||t.Events.isWebSocketInfo(e)||t.Events.isWebSocketTransfer(e)){const t=e.args.data.identifier;Ee.has(t)||(e.args.data.frame?Ee.set(t,{frame:e.args.data.frame,webSocketIdentifier:t,events:[],syntheticConnection:null}):e.args.data.workerId&&Ee.set(t,{workerId:e.args.data.workerId,webSocketIdentifier:t,events:[],syntheticConnection:null})),Ee.get(t)?.events.push(e)}},reset:function(){ye.clear(),Me.clear(),Te.clear(),Ie.length=0,we.clear(),Se.clear(),Ee.clear(),Fe.eventsByEntity.clear(),Fe.entityByEvent.clear(),Fe.createdEntityCache.clear()}});const Le=new Map,ke=new Map,be=new Map,De=new Map;function Ne(){return{profilesInProcess:ke,entryToNode:be}}function Be(e,t){const n=i.MapUtilities.getWithDefault(De,e,(()=>new Map));return i.MapUtilities.getWithDefault(n,t,(()=>({rawProfile:{startTime:0,endTime:0,nodes:[],samples:[],timeDeltas:[],lines:[]},profileId:t})))}var We=Object.freeze({__proto__:null,data:Ne,finalize:async function(e={}){!function(e){for(const[s,r]of De)for(const[o,c]of r){const l=c.threadId;if(!c.rawProfile.nodes.length||void 0===l)continue;const d=[],u=new a.CPUProfileDataModel.CPUProfileDataModel(c.rawProfile),f=n.TreeHelpers.makeEmptyTraceEntryTree();f.maxDepth=u.maxDepth;const m={rawProfile:c.rawProfile,parsedProfile:u,profileCalls:[],profileTree:f,profileId:o};function g(){u.forEachFrame((function(e,i,a,r){if(void 0===l)return;const c=n.Timing.milliToMicro(t.Timing.Milli(r)),u=i.id,f=n.Trace.makeProfileCall(i,o,a,c,s,l);m.profileCalls.push(f),d.push(m.profileCalls.length-1);const g=n.TreeHelpers.makeEmptyTraceEntryNode(f,u);be.set(f,g),g.depth=e,1===d.length&&m.profileTree?.roots.add(g)}),(function(e,i,s,a,r,c){const l=d.pop(),u=void 0!==l&&m.profileCalls[l];if(!u)return;const{callFrame:f,ts:g,pid:p,tid:h}=u,v=be.get(u);if(void 0===f||void 0===g||void 0===p||void 0===o||void 0===h||void 0===v)return;const E=n.Timing.milliToMicro(t.Timing.Milli(r)),T=n.Timing.milliToMicro(t.Timing.Milli(c));u.dur=E,v.selfTime=T;const y=d.at(-1),M=void 0!==y&&m.profileCalls.at(y),I=M&&be.get(M);I&&(v.parent=I,I.children.push(v))}))}i.MapUtilities.getWithDefault(ke,s,(()=>new Map)).set(l,m),e.isCPUProfile&&g()}}(e)},getProfileCallFunctionName:function(e,t){const n=e.profilesInProcess.get(t.pid)?.get(t.tid),i=n?.parsedProfile.nodeById(t.nodeId);return i?.functionName?i.functionName:t.callFrame.functionName},handleEvent:function(e){if(t.Events.isSyntheticCpuProfile(e)){const t=e.pid,n=e.tid,i=Be(t,"0x1");return i.rawProfile=e.args.data.cpuProfile,void(i.threadId=n)}if(t.Events.isProfile(e)){const t=Be(e.pid,e.id);return t.rawProfile.startTime=e.ts,void(t.threadId=e.tid)}if(t.Events.isProfileChunk(e)){const t=Be(e.pid,e.id).rawProfile,n=e.args?.data?.cpuProfile||{samples:[]},i=n?.samples||[],s=e.args?.data?.cpuProfile?.trace_ids||{},a=[];for(const e of n?.nodes||[]){const t=void 0===e.callFrame.lineNumber?-1:e.callFrame.lineNumber,n=void 0===e.callFrame.columnNumber?-1:e.callFrame.columnNumber,i=String(e.callFrame.scriptId),s=e.callFrame.url||"",r={...e,callFrame:{...e.callFrame,url:s,lineNumber:t,columnNumber:n,scriptId:i}};a.push(r)}const r=e.args.data?.timeDeltas||[],o=e.args.data?.lines||Array(i.length).fill(0);if(t.nodes.push(...a),t.samples?.push(...i),t.timeDeltas?.push(...r),t.lines?.push(...o),t.traceIds={...t.traceIds||{},...s},t.samples&&t.timeDeltas&&t.samples.length!==t.timeDeltas.length)return void console.error("Failed to parse CPU profile.");if(!t.endTime&&t.timeDeltas){const e=t.timeDeltas;t.endTime=e.reduce(((e,t)=>e+t),t.startTime)}}else;},reset:function(){Le.clear(),De.clear(),ke.clear(),be.clear()}});const Ue=new Map;let Oe={eventsByEntity:new Map,entityByEvent:new Map,createdEntityCache:new Map};const xe=Array(),ze=new Map;let Ae=[];const qe=[];let je=t.Configuration.defaults();const He=()=>({url:null,isOnMainFrame:!1,threads:new Map}),$e=()=>({name:null,entries:[],profileCalls:[],layoutEvents:[],updateLayoutTreeEvents:[]}),Ge=(e,t)=>i.MapUtilities.getWithDefault(e,t,He),Ve=(e,t)=>i.MapUtilities.getWithDefault(e.threads,t,$e);function Xe(){return{processes:new Map(Ue),compositorTileWorkers:new Map(Ye()),entryToNode:new Map(ze),allTraceEntries:[...Ae],entityMappings:{entityByEvent:new Map(Oe.entityByEvent),eventsByEntity:new Map(Oe.eventsByEntity),createdEntityCache:new Map(Oe.createdEntityCache)}}}function Ye(){const e=new Map;for(const t of xe){const n=e.get(t.pid)||[];n.push(t.tid),e.set(t.pid,n)}return e}function Ke(e,t,n,i){Qe(e,n),Je(e,t,n),Ze(e,n,i)}function Qe(e,t){for(const n of t.values())for(const[t,i]of n)for(const n of i.flat()){const i=Ge(e,t);if(null===i.url||"about:blank"===i.url)try{new URL(n.frame.url),i.url=n.frame.url}catch{i.url=null}}}function Je(e,t,n){for(const[i,s]of n)for(const[n]of s){const s=Ge(e,n);i===t&&(s.isOnMainFrame=!0)}}function Ze(e,t,n){for(const[t,i]of e)for(const[e,s]of n.get(t)??[]){Ve(i,e).name=s?.args.name??`${e}`}}function et(e){if(s.Runtime.experiments.isEnabled("react-native-specific-ui"))return;const t=j().worklets;if(!ge().traceIsGeneric)for(const[n,i]of e)if(null!==i.url);else{const s=t.get(n);s?i.url=s.host:e.delete(n)}}function tt(e){for(const[,t]of e)for(const[e,n]of t.threads)n.tree?.roots.size||t.threads.delete(e)}function nt(e,t){const i=Ne();for(const[s,a]of e)for(const[e,r]of a.threads){if(!r.entries.length){r.tree=n.TreeHelpers.makeEmptyTraceEntryTree();continue}n.Trace.sortTraceEventsInPlace(r.entries);const a=i.profilesInProcess.get(s)?.get(e);if(a){const t=a.parsedProfile,i=t&&new n.SamplesIntegrator.SamplesIntegrator(t,a.profileId,s,e,je),o=i?.buildProfileCalls(r.entries);if(i&&o){Ae=[...Ae,...o],r.entries=n.Trace.mergeEventsInOrder(r.entries,o),r.profileCalls=o;const e=i.jsSampleEvents;e&&(Ae=[...Ae,...e],r.entries=n.Trace.mergeEventsInOrder(r.entries,e))}}const o=n.TreeHelpers.treify(r.entries,t);r.tree=o.tree;for(const[e,t]of o.entryToNode)ze.set(e,t),u(e,Oe)}}function it(e){if(t.Events.isEnd(e)){const n=qe.pop();return n?n.name!==e.name||n.cat!==e.cat?(console.error("Begin/End events mismatch at "+n.ts+" ("+n.name+") vs. "+e.ts+" ("+e.name+")"),null):(n.dur=t.Timing.Micro(e.ts-n.ts),null):null}const n={...e,ph:"X",dur:t.Timing.Micro(0)};return qe.push(n),n}var st=Object.freeze({__proto__:null,assignIsMainFrame:Je,assignMeta:Ke,assignOrigin:Qe,assignThreadName:Ze,buildHierarchy:nt,data:Xe,deps:function(){return["Meta","Samples","AuctionWorklets","NetworkRequests"]},finalize:async function(){const{mainFrameId:e,rendererProcessesByFrame:t,threadsInProcess:i}=ge();Oe=Re().entityMappings,Ke(Ue,e,t,i),et(Ue),nt(Ue),tt(Ue),n.Trace.sortTraceEventsInPlace(Ae)},handleEvent:function(e){if(t.Events.isThreadName(e)&&e.args.name?.startsWith("CompositorTileWorker")&&xe.push({pid:e.pid,tid:e.tid}),t.Events.isBegin(e)||t.Events.isEnd(e)){const t=Ge(Ue,e.pid),n=Ve(t,e.tid),i=it(e);if(!i)return;return n.entries.push(i),void Ae.push(i)}if(t.Events.isInstant(e)||t.Events.isComplete(e)){const t=Ge(Ue,e.pid);Ve(t,e.tid).entries.push(e),Ae.push(e)}if(t.Events.isLayout(e)){const t=Ge(Ue,e.pid);Ve(t,e.tid).layoutEvents.push(e)}if(t.Events.isUpdateLayoutTree(e)){const t=Ge(Ue,e.pid);Ve(t,e.tid).updateLayoutTreeEvents.push(e)}},handleUserConfig:function(e){je=e},makeCompleteEvent:it,reset:function(){Ue.clear(),ze.clear(),Oe.eventsByEntity.clear(),Oe.entityByEvent.clear(),Oe.createdEntityCache.clear(),Ae.length=0,qe.length=0,xe.length=0},sanitizeProcesses:et,sanitizeThreads:tt});const at=new Map,rt=new Map;function ot(e,n){let i=n.get(e)?.parent;for(;i;){if(t.Events.isProfileCall(i.entry)||ct(i.entry))return i.entry;i=i.parent}return null}function ct(e){const n=t.Events.isConsoleRunTask(e),i=e.name.startsWith("v8")||e.name.startsWith("V8");return t.Events.isJSInvocationEvent(e)&&(n||!i)}function lt(e,n){return ut(e,n,ct,t.Events.isDebuggerAsyncTaskRun).at(0)}function dt(e,n){return ut(e,n,t.Events.isProfileCall,t.Events.isDebuggerAsyncTaskRun)}function ut(e,t,n,i){const s=t.get(e);if(!s)return[];const a=[[...s.children]],r=[];for(let e=0;e<a.length;e++){const t=a[e];for(let e=0;e<t.length;e++){const s=t[e];n(s.entry)?r.push(s.entry):i(s.entry)||a.push([...s.children])}}return r}function ft(){return{schedulerToRunEntryPoints:at,asyncCallToScheduler:rt}}var mt=Object.freeze({__proto__:null,data:ft,deps:function(){return["Renderer","Flows"]},finalize:async function(){const{flows:e}=N(),{entryToNode:n}=Xe();for(const s of e){const e=s.at(0);if(!e||!t.Events.isDebuggerAsyncTaskScheduled(e))continue;const a=e.args.taskName,r=s.at(1);if(!r||!t.Events.isDebuggerAsyncTaskRun(r))continue;const o=ot(e,n);if(!o)continue;const c=lt(r,n);if(!c)continue;i.MapUtilities.getWithDefault(at,o,(()=>[])).push(c);const l=dt(r,n);for(const e of l)rt.set(e,{taskName:a,scheduler:o})}},handleEvent:function(e){},reset:function(){at.clear(),rt.clear()}});const gt=new Map;var pt=Object.freeze({__proto__:null,data:function(){return{domStatsByFrameId:gt}},finalize:async function(){},handleEvent:function(e){if(!t.Events.isDOMStats(e))return;i.MapUtilities.getWithDefault(gt,e.args.data.frame,(()=>[])).push(e)},reset:function(){gt.clear()}});let ht=[];const vt=new Map,Et=[],Tt=[],yt=[],Mt=[];const It=["workerStart","redirectStart","redirectEnd","fetchStart","domainLookupStart","domainLookupEnd","connectStart","connectEnd","secureConnectionStart","requestStart","responseStart","responseEnd","navigationStart","unloadEventStart","unloadEventEnd","redirectStart","redirectEnd","fetchStart","commitNavigationEnd","domainLookupStart","domainLookupEnd","connectStart","connectEnd","secureConnectionStart","requestStart","responseStart","responseEnd","domLoading","domInteractive","domContentLoadedEventStart","domContentLoadedEventEnd","domComplete","loadEventStart","loadEventEnd"];function wt(){return{performanceMeasures:ht.filter((e=>"blink.user_timing"===e.cat)),consoleTimings:ht.filter((e=>"blink.console"===e.cat)),performanceMarks:[...Tt],timestampEvents:[...Mt],measureTraceByTraceId:new Map(vt)}}var St=Object.freeze({__proto__:null,data:wt,finalize:async function(){const e=[...Et,...yt];ht=n.Trace.createMatchedSortedSyntheticEvents(e),ht=ht.sort(((e,t)=>function(e,t,n){const i=e.ts,s=t.ts;if(i<s)return-1;if(i>s)return 1;const a=i+(e.dur??0),r=s+(t.dur??0);return a>r?-1:a<r?1:n.indexOf(t)-n.indexOf(e)}(e,t,[...ht])))},handleEvent:function(e){It.includes(e.name)||(t.Events.isUserTimingMeasure(e)&&vt.set(e.args.traceId,e),t.Events.isPerformanceMeasure(e)?Et.push(e):(t.Events.isPerformanceMark(e)&&Tt.push(e),t.Events.isConsoleTime(e)&&yt.push(e),t.Events.isConsoleTimeStamp(e)&&Mt.push(e)))},reset:function(){ht.length=0,Et.length=0,Tt.length=0,yt.length=0,Mt.length=0,vt.clear()}});const Ft=[],Pt=[],_t=[],Rt=new Map,Ct=new Map,Lt=[];function kt(){const e=wt().timestampEvents;for(const i of e){if(!i.args.data)continue;const e=String(i.args.data.name??i.args.data.message);Ct.set(e,i);const s=Nt(i),a=i.args.data.start,r=i.args.data.end;if(!s&&!a&&!r)continue;const o="number"==typeof a?t.Timing.Micro(a):Ct.get(String(a))?.ts,c="number"==typeof r?t.Timing.Micro(r):Ct.get(String(r))?.ts;if(void 0!==c&&void 0===o)continue;const l=o??i.ts,d=c??i.ts;if(s){const a={...i,name:e,cat:"devtools.extension",args:s,rawSourceEvent:i,dur:t.Timing.Micro(d-l),ts:l,ph:"X"},r=n.SyntheticEvents.SyntheticEventsManager.getActiveManager().registerSyntheticEvent(a);Ft.push(r);continue}const u={...i,name:e,cat:"disabled-by-default-v8.inspector",ph:"X",ts:l,dur:t.Timing.Micro(d-l),rawSourceEvent:i},f=n.SyntheticEvents.SyntheticEventsManager.getActiveManager().registerSyntheticEvent(u);Lt.push(f)}}function bt(e){for(const i of e){const e=Dt(i);if(!e)continue;const s={name:i.name,ph:t.Extensions.isExtensionPayloadMarker(e)?"I":"X",pid:i.pid,tid:i.tid,ts:i.ts,dur:i.dur,cat:"devtools.extension",args:e,rawSourceEvent:t.Events.isSyntheticUserTiming(i)?i.rawSourceEvent:i};if(t.Extensions.isExtensionPayloadMarker(e)){const e=n.SyntheticEvents.SyntheticEventsManager.getActiveManager().registerSyntheticEvent(s);_t.push(e)}else if(t.Extensions.isExtensionPayloadTrackEntry(s.args)){const e=n.SyntheticEvents.SyntheticEventsManager.getActiveManager().registerSyntheticEvent(s);Ft.push(e)}else;}}function Dt(e){const n=t.Events.isPerformanceMark(e)?e.args.data?.detail:e.args.data.beginEvent.args.detail;if(!n)return null;try{const e=JSON.parse(n);return"devtools"in e&&t.Extensions.isValidExtensionPayload(e.devtools)?e.devtools:null}catch{return null}}function Nt(e){if(!e.args.data)return null;const t=e.args.data.track;return""===t||void 0===t?null:{color:String(e.args.data.color),track:String(t),dataType:"track-entry",trackGroup:void 0!==e.args.data.trackGroup?String(e.args.data.trackGroup):void 0}}var Bt=Object.freeze({__proto__:null,data:function(){return{entryToNode:Rt,extensionTrackData:Pt,extensionMarkers:_t,syntheticConsoleEntriesForTimingsTrack:Lt}},deps:function(){return["UserTimings"]},extensionDataInConsoleTimeStamp:Nt,extensionDataInPerformanceTiming:Dt,extractConsoleAPIExtensionEntries:kt,extractPerformanceAPIExtensionEntries:bt,finalize:async function(){!function(){const e=wt().performanceMeasures,t=wt().performanceMarks;bt(n.Trace.mergeEventsInOrder(e,t)),kt(),n.Trace.sortTraceEventsInPlace(Ft),n.Extensions.buildTrackDataFromExtensionEntries(Ft,Pt,Rt)}()},handleEvent:function(e){},reset:function(){Ft.length=0,Lt.length=0,Pt.length=0,_t.length=0,Rt.clear(),Ct.clear()}});const Wt=[],Ut=[],Ot=new Map;let xt={},zt=null;const At=[],qt=[];function jt(){return{paints:Wt,snapshots:Ut,paintsToSnapshots:Ot}}var Ht=Object.freeze({__proto__:null,data:jt,deps:function(){return["Meta"]},finalize:async function(){const e=ge();n.Trace.sortTraceEventsInPlace(qt);for(const n of qt)if(t.Events.isSetLayerId(n)){if(e.mainFrameId!==n.args.data.frame)continue;zt=n.args.data.layerTreeId}else if(t.Events.isUpdateLayer(n))At.push(n);else{if(t.Events.isPaint(n)){if(!n.args.data.layerId)continue;Wt.push(n),xt[n.args.data.layerId]=n;continue}if(t.Events.isDisplayListItemListSnapshot(n)){let e=null;for(let t=At.length-1;t>-1;t--){const i=At[t];if(i.pid===n.pid&&i.tid===n.tid){e=i;break}}if(!e)continue;if(e.args.layerTreeId!==zt)continue;const t=xt[e.args.layerId];if(!t)continue;Ut.push(n),Ot.set(t,n)}}},handleEvent:function(e){(t.Events.isPaint(e)||t.Events.isDisplayListItemListSnapshot(e)||t.Events.isUpdateLayer(e)||t.Events.isSetLayerId(e))&&qt.push(e)},reset:function(){Wt.length=0,Ut.length=0,Ot.clear(),xt={},zt=null,At.length=0,qt.length=0}});function $t(e,t,n){let i="OTHER";return"CrRendererMain"===t.name?i="MAIN_THREAD":"DedicatedWorker thread"===t.name?i="WORKER":t.name?.startsWith("CompositorTileWorker")?i="RASTERIZER":n.worklets.has(e)?i="AUCTION_WORKLET":t.name?.startsWith("ThreadPool")&&(i="THREAD_POOL"),i}function Gt(e,t){const n=[];if(e.processes.size)for(const[i,s]of e.processes)for(const[a,r]of s.threads){if(!r.tree)continue;const o=$t(i,r,t);n.push({name:r.name,pid:i,tid:a,processIsOnMainFrame:s.isOnMainFrame,entries:r.entries,tree:r.tree,type:o,entryToNode:e.entryToNode})}return n}const Vt=new WeakMap;var Xt=Object.freeze({__proto__:null,threadsInRenderer:Gt,threadsInTrace:function(e){const t=Vt.get(e);if(t)return t;const n=Gt(e.Renderer,e.AuctionWorklets);if(n.length)return Vt.set(e,n),n;const i=[];if(e.Samples.profilesInProcess.size)for(const[t,n]of e.Samples.profilesInProcess)for(const[s,a]of n)a.profileTree&&i.push({pid:t,tid:s,name:null,entries:a.profileCalls,processIsOnMainFrame:!1,tree:a.profileTree,type:"CPU_PROFILE",entryToNode:e.Samples.entryToNode});return Vt.set(e,i),i}});const Yt=[];let Kt=null;class Qt{#e=[];#t={};#n=new sn;#i=null;#s=!1;#a=!1;#r=null;#o=null;#c=null;#l=null;#d=null;#u=null;#f=null;#m=null;#g=null;#p;constructor(e,t,n,i,s){const a=Gt(t,n).filter((e=>"MAIN_THREAD"===e.type&&e.processIsOnMainFrame)).map((e=>({tid:e.tid,pid:e.pid,startTime:e.entries[0].ts})));this.#p=s,this.#h(e,a,i.mainFrameId)}framesById(){return this.#t}frames(){return this.#e}#v(e,t){this.#i||this.#E(e,t),this.#l=e,this.#n.addFrameIfNotExists(t,e,!1,!1)}#T(e,t,n){this.#i||this.#E(e,t),this.#n.addFrameIfNotExists(t,e,!0,n),this.#n.setDropped(t,!0),this.#n.setPartial(t,n)}#y(e,t){if(this.#i){if(this.#s||!this.#a){if(this.#d){(this.#o?this.#o.triggerTime:this.#l||this.#d)>this.#i.startTime&&(this.#i.idle=!0,this.#l=null),this.#d=null}const e=this.#n.processPendingBeginFramesOnDrawFrame(t);for(const n of e){const e=this.#i.idle;this.#E(n.startTime,t),e&&this.#o&&this.#M(),n.isDropped&&(this.#i.dropped=!0),n.isPartial&&(this.#i.isPartial=!0)}}this.#s=!1}else this.#E(e,t)}#I(){this.#i&&this.#o&&!this.#d&&this.#M()}#w(){this.#i&&(this.#a=!0)}#S(){this.#c&&(this.#o=this.#c,this.#c=null,this.#a=!1,this.#s=!0)}#F(e){this.#r=e}#P(e,t){t&&(this.#d=e)}#E(e,n){this.#i&&this.#_(this.#i,e),this.#i=new Zt(n,e,t.Timing.Micro(e-ge().traceBounds.min))}#_(e,t){e.setLayerTree(this.#r),e.setEndTime(t),this.#r&&(this.#r.paints=e.paints);const n=this.#e[this.#e.length-1];this.#e.length&&n&&(e.startTime!==n.endTime||e.startTime>e.endTime)&&console.assert(!1,`Inconsistent frame time for frame ${this.#e.length} (${e.startTime} - ${e.endTime})`);const i=this.#e.push(e);e.setIndex(i-1),"number"==typeof e.mainFrameId&&(this.#t[e.mainFrameId]=e)}#M(){this.#o&&this.#i&&(this.#i.paints=this.#o.paints,this.#i.mainFrameId=this.#o.mainFrameId,this.#o=null)}#h(e,t,n){let i=0;this.#g=t.length&&t[0].tid||null,this.#m=t.length&&t[0].pid||null;for(let s=0;s<e.length;++s){for(;i+1<t.length&&t[i+1].startTime<=e[s].ts;)this.#g=t[++i].tid,this.#m=t[i].pid;this.#R(e[s],n)}this.#g=null,this.#m=null}#R(e,n){t.Events.isSetLayerId(e)&&e.args.data.frame===n?this.#f=e.args.data.layerTreeId:t.Events.isLayerTreeHostImplSnapshot(e)&&Number(e.id)===this.#f?this.#F({entry:e,paints:[]}):(function(e){return t.Events.isSetLayerId(e)||t.Events.isBeginFrame(e)||t.Events.isDroppedFrame(e)||t.Events.isRequestMainThreadFrame(e)||t.Events.isBeginMainThreadFrame(e)||t.Events.isNeedsBeginFrameChanged(e)||t.Events.isCommit(e)||t.Events.isCompositeLayers(e)||t.Events.isActivateLayerTree(e)||t.Events.isDrawFrame(e)}(e)&&this.#C(e),e.tid===this.#g&&e.pid===this.#m&&this.#L(e))}#C(e){e.args.layerTreeId===this.#f&&(t.Events.isBeginFrame(e)?this.#v(e.ts,e.args.frameSeqId):t.Events.isDrawFrame(e)?this.#y(e.ts,e.args.frameSeqId):t.Events.isActivateLayerTree(e)?this.#I():t.Events.isRequestMainThreadFrame(e)?this.#w():t.Events.isNeedsBeginFrameChanged(e)?this.#P(e.ts,e.args.data&&Boolean(e.args.data.needsBeginFrame)):t.Events.isDroppedFrame(e)&&this.#T(e.ts,e.args.frameSeqId,Boolean(e.args.hasPartialUpdate)))}#L(e){if(function(e){return"RunTask"===e.name&&e.cat.includes("disabled-by-default-devtools.timeline")}(e)&&(this.#u=e.ts),!this.#c&&Jt.has(e.name)&&(this.#c=new tn(this.#u||e.ts)),this.#c){if(t.Events.isBeginMainThreadFrame(e)&&e.args.data.frameId&&(this.#c.mainFrameId=e.args.data.frameId),t.Events.isPaint(e)){const t=this.#p.paintsToSnapshots.get(e);t&&this.#c.paints.push(new en(e,t))}(t.Events.isCompositeLayers(e)||t.Events.isCommit(e))&&e.args.layerTreeId===this.#f&&this.#S()}}}const Jt=new Set(["ScheduleStyleRecalculation","InvalidateLayout","BeginMainThreadFrame","ScrollLayer"]);class Zt{cat="devtools.legacy_frame";name="frame";ph="X";ts;pid=t.Events.ProcessID(-1);tid=t.Events.ThreadID(-1);index=-1;startTime;startTimeOffset;endTime;duration;idle;dropped;isPartial;layerTree;paints;mainFrameId;seqId;constructor(e,n,i){this.seqId=e,this.startTime=n,this.ts=n,this.startTimeOffset=i,this.endTime=this.startTime,this.duration=t.Timing.Micro(0),this.idle=!1,this.dropped=!1,this.isPartial=!1,this.layerTree=null,this.paints=[],this.mainFrameId=void 0}setIndex(e){this.index=e}setEndTime(e){this.endTime=e,this.duration=t.Timing.Micro(this.endTime-this.startTime)}setLayerTree(e){this.layerTree=e}get dur(){return this.duration}}class en{#k;#b;constructor(e,t){this.#k=e,this.#b=t}layerId(){return this.#k.args.data.layerId}event(){return this.#k}picture(){const e=this.#b.args.snapshot.params?.layer_rect,t=this.#b.args.snapshot.skp64;return e&&t?{rect:e,serializedPicture:t}:null}}class tn{paints;mainFrameId;triggerTime;constructor(e){this.paints=[],this.mainFrameId=void 0,this.triggerTime=e}}class nn{seqId;startTime;isDropped;isPartial;constructor(e,t,n,i){this.seqId=e,this.startTime=t,this.isDropped=n,this.isPartial=i}}class sn{queueFrames=[];mapFrames={};addFrameIfNotExists(e,t,n,i){e in this.mapFrames||(this.mapFrames[e]=new nn(e,t,n,i),this.queueFrames.push(e))}setDropped(e,t){e in this.mapFrames&&(this.mapFrames[e].isDropped=t)}setPartial(e,t){e in this.mapFrames&&(this.mapFrames[e].isPartial=t)}processPendingBeginFramesOnDrawFrame(e){const t=[];if(e in this.mapFrames){for(;this.queueFrames[0]!==e;){const e=this.queueFrames[0];this.mapFrames[e].isDropped&&t.push(this.mapFrames[e]),delete this.mapFrames[e],this.queueFrames.shift()}t.push(this.mapFrames[e]),delete this.mapFrames[e],this.queueFrames.shift()}return t}}var an=Object.freeze({__proto__:null,LayerPaintEvent:en,PendingFrame:tn,TimelineFrameBeginFrameQueue:sn,TimelineFrameModel:Qt,data:function(){return{frames:Kt?Array.from(Kt.frames()):[],framesById:Kt?{...Kt.framesById()}:{}}},deps:function(){return["Meta","Renderer","AuctionWorklets","LayerTree"]},finalize:async function(){n.Trace.sortTraceEventsInPlace(Yt);const e=new Qt(Yt,Xe(),j(),ge(),jt());Kt=e},framesWithinWindow:function(e,t,n){const s=i.ArrayUtilities.lowerBound(e,t||0,((e,t)=>e-t.endTime)),a=i.ArrayUtilities.lowerBound(e,n||1/0,((e,t)=>e-t.startTime));return e.slice(s,a)},handleEvent:function(e){Yt.push(e)},reset:function(){Yt.length=0}});const rn=new Map;let on=[];var cn=Object.freeze({__proto__:null,data:function(){return{mainGPUThreadTasks:on}},deps:function(){return["Meta"]},finalize:async function(){const{gpuProcessId:e,gpuThreadId:t}=ge(),n=rn.get(e);n&&t&&(on=n.get(t)||[])},handleEvent:function(e){t.Events.isGPUTask(e)&&n.Trace.addEventToProcessThread(e,rn)},reset:function(){rn.clear(),on=[]}});const ln=new Map,dn=new Map,un=new Map,fn=new Map,mn=new Map;var gn=Object.freeze({__proto__:null,data:function(){return{paintImageByDrawLazyPixelRef:un,paintImageForEvent:fn,paintImageEventForUrl:mn}},finalize:async function(){},handleEvent:function(e){if(t.Events.isPaintImage(e)){const t=ln.get(e.pid)||new Map,n=t.get(e.tid)||[];if(n.push(e),t.set(e.tid,n),ln.set(e.pid,t),e.args.data.url){i.MapUtilities.getWithDefault(mn,e.args.data.url,(()=>[])).push(e)}}else{if(t.Events.isDecodeLazyPixelRef(e)&&void 0!==e.args?.LazyPixelRef){const t=dn.get(e.pid)||new Map,n=t.get(e.tid)||[];n.push(e),t.set(e.tid,n),dn.set(e.pid,t)}if(t.Events.isDrawLazyPixelRef(e)&&void 0!==e.args?.LazyPixelRef){const t=ln.get(e.pid)?.get(e.tid)?.at(-1);if(!t)return;un.set(e.args.LazyPixelRef,t)}else if(t.Events.isDecodeImage(e)){const t=ln.get(e.pid)?.get(e.tid)?.at(-1);if(t)return void fn.set(e,t);const n=dn.get(e.pid)?.get(e.tid)?.at(-1);if(void 0===n?.args?.LazyPixelRef)return;const i=un.get(n.args.LazyPixelRef);if(!i)return;fn.set(e,i)}}},reset:function(){ln.clear(),dn.clear(),un.clear(),fn.clear(),mn.clear()}});const pn=new Map,hn=new Map,vn=new Map,En=new Map,Tn=new Map,yn=new Map,Mn=new Map;function In(e){En.set(e.event,e.initiator);const t=Tn.get(e.initiator)||[];t.push(e.event),Tn.set(e.initiator,t)}var wn=Object.freeze({__proto__:null,data:function(){return{eventToInitiator:En,initiatorToEvents:Tn}},deps:function(){return["Flows","AsyncJSCalls"]},finalize:async function(){!function(){const e=N().flows;for(let t=0;t<e.length;t++){const n=e[t];for(let e=0;e<n.length-1;e++)In({event:n[e+1],initiator:n[e]})}}(),function(){const e=ft().schedulerToRunEntryPoints.entries();for(const[t,n]of e)for(const e of n)In({event:e,initiator:t})}()},handleEvent:function(e){if(t.Events.isScheduleStyleRecalculation(e))pn.set(e.args.data.frame,e);else if(t.Events.isUpdateLayoutTree(e)){if(e.args.beginData){vn.set(e.args.beginData.frame,e);const t=pn.get(e.args.beginData.frame);t&&In({event:e,initiator:t})}}else if(t.Events.isInvalidateLayout(e)){let t=e;if(!hn.has(e.args.data.frame)){const i=vn.get(e.args.data.frame);if(i){const{endTime:s}=n.Timing.eventTimingsMicroSeconds(i),a=En.get(i);a&&s&&s>e.ts&&(t=a)}}hn.set(e.args.data.frame,t)}else if(t.Events.isLayout(e)){const t=hn.get(e.args.beginData.frame);t&&In({event:e,initiator:t}),hn.delete(e.args.beginData.frame)}else if(t.Events.isWebSocketCreate(e))yn.set(e.args.data.identifier,e);else if(t.Events.isWebSocketInfo(e)||t.Events.isWebSocketTransfer(e)){const t=yn.get(e.args.data.identifier);t&&In({event:e,initiator:t})}else if(t.Events.isSchedulePostTaskCallback(e))Mn.set(e.args.data.taskId,e);else if(t.Events.isRunPostTaskCallback(e)||t.Events.isAbortPostTaskCallback(e)){const t=Mn.get(e.args.data.taskId);t&&In({event:e,initiator:t})}},reset:function(){pn.clear(),hn.clear(),vn.clear(),En.clear(),Tn.clear(),yn.clear(),Mn.clear()}});const Sn=new Map,Fn=new Map;let Pn=null,_n=!1;const Rn=[];let Cn=null;function Ln(e,t){const n=Sn.get(e)||[];n.push(t),null!==Cn&&n.length>Cn&&n.shift(),Sn.set(e,n);const i=Fn.get(e)??0;Fn.set(e,i+1)}var kn=Object.freeze({__proto__:null,data:function(){return{invalidationsForEvent:Sn,invalidationCountForEvent:Fn}},finalize:async function(){},handleEvent:function(e){if(0!==Cn)if(t.Events.isUpdateLayoutTree(e)){Pn=e;for(const n of Rn){if(t.Events.isLayoutInvalidationTracking(n))continue;const i=Pn.args.beginData?.frame;i&&n.args.data.frame===i&&Ln(e,n)}}else if(t.Events.isInvalidationTracking(e)){if(_n&&(Rn.length=0,Pn=null,_n=!1),Pn&&(t.Events.isScheduleStyleInvalidationTracking(e)||t.Events.isStyleRecalcInvalidationTracking(e)||t.Events.isStyleInvalidatorInvalidationTracking(e))){const t=Pn.ts+(Pn.dur||0);e.ts>=Pn.ts&&e.ts<=t&&Pn.args.beginData?.frame===e.args.data.frame&&Ln(Pn,e)}Rn.push(e)}else if(t.Events.isPaint(e))_n=!0;else if(t.Events.isLayout(e)){const n=e.args.beginData.frame;for(const i of Rn)t.Events.isLayoutInvalidationTracking(i)&&i.args.data.frame===n&&Ln(e,i)}},handleUserConfig:function(e){Cn=e.maxInvalidationEventsPerEvent},reset:function(){Sn.clear(),Fn.clear(),Pn=null,Rn.length=0,_n=!1,Cn=null}});const bn=new Map;let Dn=[];let Nn=[];const Bn=new Set;function Wn(e,s){const a=e.args.data?.navigationId;if(!a)throw new Error("Navigation event unexpectedly had no navigation ID.");const r=On(s),{rendererProcessesByFrame:o}=ge(),c=o.get(r);if(!c)return;if(c.get(s.pid)&&!t.Events.isNavigationStart(s))if(t.Events.isFirstContentfulPaint(s)){const n=t.Timing.Micro(s.ts-e.ts);Un(r,a,{event:s,metricName:"FCP",classification:zn(n),navigation:e,timing:n})}else if(t.Events.isFirstPaint(s)){Un(r,a,{event:s,metricName:"FP",classification:"unclassified",navigation:e,timing:t.Timing.Micro(s.ts-e.ts)})}else if(t.Events.isMarkDOMContent(s)){const n=t.Timing.Micro(s.ts-e.ts);Un(r,a,{event:s,metricName:"DCL",classification:"unclassified",navigation:e,timing:n})}else if(t.Events.isInteractiveTime(s)){const i=t.Timing.Micro(s.ts-e.ts);Un(r,a,{event:s,metricName:"TTI",classification:An(i),navigation:e,timing:i});const o=n.Timing.milliToMicro(t.Timing.Milli(s.args.args.total_blocking_time_ms));Un(r,a,{event:s,metricName:"TBT",classification:Hn(o),navigation:e,timing:o})}else if(t.Events.isMarkLoad(s)){Un(r,a,{event:s,metricName:"L",classification:"unclassified",navigation:e,timing:t.Timing.Micro(s.ts-e.ts)})}else if(t.Events.isLargestContentfulPaintCandidate(s)){const n=s.args.data?.candidateIndex;if(!n)throw new Error("Largest Contenful Paint unexpectedly had no candidateIndex.");const o=t.Timing.Micro(s.ts-e.ts),c={event:s,metricName:"LCP",classification:qn(o),navigation:e,timing:o},l=i.MapUtilities.getWithDefault(bn,r,(()=>new Map)),d=i.MapUtilities.getWithDefault(l,a,(()=>new Map)).get("LCP");if(void 0===d)return Bn.add(c.event),void Un(r,a,c);const u=d.event;if(!t.Events.isLargestContentfulPaintCandidate(u))return;const f=u.args.data?.candidateIndex;if(!f)return;f<n&&(Bn.delete(u),Bn.add(c.event),Un(r,a,c))}else if(!t.Events.isLayoutShift(s))return i.assertNever(s,`Unexpected event type: ${s}`)}function Un(e,t,n){const s=i.MapUtilities.getWithDefault(bn,e,(()=>new Map)),a=i.MapUtilities.getWithDefault(s,t,(()=>new Map));a.delete(n.metricName),a.set(n.metricName,n)}function On(e){if(t.Events.isFirstContentfulPaint(e)||t.Events.isInteractiveTime(e)||t.Events.isLargestContentfulPaintCandidate(e)||t.Events.isNavigationStart(e)||t.Events.isLayoutShift(e)||t.Events.isFirstPaint(e))return e.args.frame;if(t.Events.isMarkDOMContent(e)||t.Events.isMarkLoad(e)){const t=e.args.data?.frame;if(!t)throw new Error("MarkDOMContent unexpectedly had no frame ID.");return t}i.assertNever(e,`Unexpected event type: ${e}`)}function xn(e){if(t.Events.isFirstContentfulPaint(e)||t.Events.isLargestContentfulPaintCandidate(e)||t.Events.isFirstPaint(e)){const t=e.args.data?.navigationId;if(!t)throw new Error("Trace event unexpectedly had no navigation ID.");const{navigationsByNavigationId:n}=ge(),i=n.get(t);return i||null}if(t.Events.isMarkDOMContent(e)||t.Events.isInteractiveTime(e)||t.Events.isLayoutShift(e)||t.Events.isMarkLoad(e)){const t=On(e),{navigationsByFrameId:i}=ge();return n.Trace.getNavigationForTraceEvent(e,t,i)}return t.Events.isNavigationStart(e)?null:i.assertNever(e,`Unexpected event type: ${e}`)}function zn(e){const i=n.Timing.secondsToMicro(t.Timing.Seconds(1.8));let s="bad";return e<=n.Timing.secondsToMicro(t.Timing.Seconds(3))&&(s="ok"),e<=i&&(s="good"),s}function An(e){const i=n.Timing.secondsToMicro(t.Timing.Seconds(3.8));let s="bad";return e<=n.Timing.secondsToMicro(t.Timing.Seconds(7.3))&&(s="ok"),e<=i&&(s="good"),s}function qn(e){const i=n.Timing.secondsToMicro(t.Timing.Seconds(2.5));let s="bad";return e<=n.Timing.secondsToMicro(t.Timing.Seconds(4))&&(s="ok"),e<=i&&(s="good"),s}function jn(e){return"unclassified"}function Hn(e){const i=n.Timing.milliToMicro(t.Timing.Milli(200));let s="bad";return e<=n.Timing.milliToMicro(t.Timing.Milli(600))&&(s="ok"),e<=i&&(s="good"),s}function $n(){return{metricScoresByFrameId:bn,allMarkerEvents:Dn}}var Gn=Object.freeze({__proto__:null,data:$n,deps:function(){return["Meta"]},finalize:async function(){Nn.sort(((e,t)=>e.ts-t.ts));for(const e of Nn){const t=xn(e);t&&Wn(t,e)}const e=function(){const e=[],t=[...bn.values()].flatMap((e=>[...e.values()]));for(let n=0;n<t.length;n++){const i=t[n].get("LCP");i?.event&&e.push(i.event)}return e}(),n=ge().mainFrameId,i=[...e,...Nn.filter((e=>!t.Events.isLargestContentfulPaintCandidate(e)))].filter(t.Events.isMarkerEvent);Dn=i.filter((e=>On(e)===n)).sort(((e,t)=>e.ts-t.ts))},getFrameIdForPageLoadEvent:On,handleEvent:function(e){t.Events.eventIsPageLoadEvent(e)&&Nn.push(e)},metricIsLCP:function(e){return"LCP"===e.metricName},reset:function(){bn.clear(),Nn=[],Dn=[],Bn.clear()},scoreClassificationForDOMContentLoaded:jn,scoreClassificationForFirstContentfulPaint:zn,scoreClassificationForLargestContentfulPaint:qn,scoreClassificationForTimeToInteractive:An,scoreClassificationForTotalBlockingTime:Hn});const Vn=new Map,Xn=new Map;var Yn=Object.freeze({__proto__:null,data:function(){return{lcpRequestByNavigationId:Xn}},deps:function(){return["Meta","NetworkRequests","PageLoadMetrics"]},finalize:async function(){const e=Re().byTime,{traceBounds:n,navigationsByNavigationId:i}=ge(),s=$n().metricScoresByFrameId;for(const[a,r]of i){const i=s.get(r.args.frame)?.get(a)?.get("LCP"),o=i?.event;if(!o||!t.Events.isLargestContentfulPaintCandidate(o))continue;const c=o.args.data?.nodeId;if(!c)continue;const l=Vn.get(o.pid)?.get(c),d=l?.args.data?.imageUrl;if(!d)continue;const u=r?.ts??n.min,f=l.ts;let m;for(const t of e)if(!(t.ts<u)){if(t.ts>=f)break;if(t.args.data.url===d||t.args.data.redirects.some((e=>e.url===d))){m=t;break}}m&&Xn.set(a,m)}},handleEvent:function(e){if(!t.Events.isLargestImagePaintCandidate(e)||!e.args.data)return;i.MapUtilities.getWithDefault(Vn,e.pid,(()=>new Map)).set(e.args.data.DOMNodeId,e)},reset:function(){Vn.clear()}});const Kn=new Map;var Qn=Object.freeze({__proto__:null,data:function(){return Kn},finalize:async function(){},handleEvent:function(e){t.Events.isLargestTextPaintCandidate(e)&&e.args.data&&Kn.set(e.args.data.DOMNodeId,e)},reset:function(){Kn.clear()}});const Jn=[],Zn=[],ei=[],ti=[];let ni={};function ii(){return{legacySyntheticScreenshots:ti.length?ti:null,screenshots:ei.length?ei:null}}var si=Object.freeze({__proto__:null,data:ii,deps:function(){return["Meta"]},finalize:async function(){const e=n.Trace.createMatchedSortedSyntheticEvents(Jn);ni=Object.fromEntries(e.map((e=>[e.args.data.beginEvent.args.chrome_frame_reporter.frame_sequence,t.Timing.Micro(e.ts+e.dur)])));for(const e of Zn){const{cat:t,name:i,ph:s,pid:a,tid:r}=e,o=n.SyntheticEvents.SyntheticEventsManager.registerSyntheticEvent({rawSourceEvent:e,cat:t,name:i,ph:s,pid:a,tid:r,ts:e.ts,args:{dataUri:`data:image/jpg;base64,${e.args.snapshot}`}});ti.push(o)}},handleEvent:function(e){t.Events.isLegacyScreenshot(e)?Zn.push(e):t.Events.isScreenshot(e)?ei.push(e):t.Events.isPipelineReporter(e)&&Jn.push(e)},reset:function(){Jn.length=0,Zn.length=0,ti.length=0,ei.length=0,ni={}},screenshotImageDataUri:function(e){return t.Events.isLegacySyntheticScreenshot(e)?e.args.dataUri:`data:image/jpg;base64,${e.args.snapshot}`}});const ai=n.Timing.milliToMicro(t.Timing.Milli(5e3)),ri=n.Timing.milliToMicro(t.Timing.Milli(1e3)),oi=[],ci=[],li=[],di=[],ui=[],fi=[],mi=[],gi=[],pi=new Set,hi=[],vi=[];let Ei=0,Ti=-1;const yi=[],Mi=new Map,Ii=[];function wi(e){return{min:e,max:e,range:t.Timing.Micro(0)}}function Si(e,n){e.max=n,e.range=t.Timing.Micro(e.max-e.min)}function Fi(e){const t=ii();if(t.screenshots){const i=n.Trace.findPreviousEventBeforeTimestamp(t.screenshots,e);return{before:i,after:i?t.screenshots[t.screenshots.indexOf(i)+1]:null}}if(t.legacySyntheticScreenshots){const i=n.Trace.findPreviousEventBeforeTimestamp(t.legacySyntheticScreenshots,e);return{before:i,after:i?t.legacySyntheticScreenshots[t.legacySyntheticScreenshots.indexOf(i)+1]:null}}return{before:null,after:null}}var Pi=Object.freeze({__proto__:null,MAX_CLUSTER_DURATION:ai,MAX_SHIFT_TIME_DELTA:ri,data:function(){return{clusters:yi,sessionMaxScore:Ei,clsWindowID:Ti,prePaintEvents:hi,layoutInvalidationEvents:ci,scheduleStyleInvalidationEvents:li,styleRecalcInvalidationEvents:[],renderFrameImplCreateChildFrameEvents:ui,domLoadingEvents:fi,layoutImageUnsizedEvents:mi,remoteFonts:gi,scoreRecords:Ii,backendNodeIds:[...pi],clustersByNavigationId:new Map(Mi),paintImageEvents:vi}},deps:function(){return["Screenshots","Meta"]},finalize:async function(){oi.sort(((e,t)=>e.ts-t.ts)),hi.sort(((e,t)=>e.ts-t.ts)),ci.sort(((e,t)=>e.ts-t.ts)),ui.sort(((e,t)=>e.ts-t.ts)),fi.sort(((e,t)=>e.ts-t.ts)),mi.sort(((e,t)=>e.ts-t.ts)),gi.sort(((e,t)=>e.beginRemoteFontLoadEvent.ts-t.beginRemoteFontLoadEvent.ts)),vi.sort(((e,t)=>e.ts-t.ts)),await async function(){const{navigationsByFrameId:e,mainFrameId:s,traceBounds:a}=ge(),r=e.get(s)||[];if(0===oi.length)return;let o=oi[0].ts,c=oi[0].ts,l=null;for(const e of oi){const s=e.ts-o>ai,a=e.ts-c>ri,d=i.ArrayUtilities.nearestIndexFromEnd(r,(t=>t.ts<e.ts)),u=l!==d&&null!==d;if(s||a||u||!yi.length){const n=e.ts,i=s?o+ai:1/0,l=a?c+ri:1/0,f=u?r[d].ts:1/0,m=Math.min(i,l,f);if(yi.length>0){Si(yi[yi.length-1].clusterWindow,t.Timing.Micro(m))}const g=null===d?t.Events.NO_NAVIGATION:r[d].args.data?.navigationId;yi.push({name:"SyntheticLayoutShiftCluster",events:[],clusterWindow:wi(n),clusterCumulativeScore:0,scoreWindows:{good:wi(n)},navigationId:g,ts:e.ts,pid:e.pid,tid:e.tid,ph:"X",cat:"",dur:t.Timing.Micro(-1)}),o=n}const f=yi[yi.length-1],m=null!==d?t.Timing.Micro(e.ts-r[d].ts):void 0;if(f.clusterCumulativeScore+=e.args.data?e.args.data.weighted_score_delta:0,!e.args.data)continue;const g=n.SyntheticEvents.SyntheticEventsManager.registerSyntheticEvent({rawSourceEvent:e,...e,name:"SyntheticLayoutShift",args:{frame:e.args.frame,data:{...e.args.data,rawEvent:e,navigationId:f.navigationId??void 0}},parsedData:{timeFromNavigation:m,screenshots:Fi(e.ts),cumulativeWeightedScoreInWindow:f.clusterCumulativeScore,sessionWindowData:{cumulativeWindowScore:0,id:yi.length}}});f.events.push(g),Si(f.clusterWindow,e.ts),c=e.ts,l=d}for(const e of yi){let s=0,o=-1;if(e===yi[yi.length-1]){const n=ai+e.clusterWindow.min,s=e.clusterWindow.max+ri,o=i.ArrayUtilities.nearestIndexFromBeginning(r,(t=>t.ts>e.clusterWindow.max)),c=o?r[o].ts:1/0,l=Math.min(n,s,a.max,c);Si(e.clusterWindow,t.Timing.Micro(l))}let c=0,l=null;for(const n of e.events){s+=n.args.data?n.args.data.weighted_score_delta:0,o=n.parsedData.sessionWindowData.id;const i=n.ts;n.parsedData.sessionWindowData.cumulativeWindowScore=e.clusterCumulativeScore,s<.1?Si(e.scoreWindows.good,i):s>=.1&&s<.25?(e.scoreWindows.needsImprovement||(Si(e.scoreWindows.good,t.Timing.Micro(i-1)),e.scoreWindows.needsImprovement=wi(i)),Si(e.scoreWindows.needsImprovement,i)):s>=.25&&(e.scoreWindows.bad||(e.scoreWindows.needsImprovement?Si(e.scoreWindows.needsImprovement,t.Timing.Micro(i-1)):Si(e.scoreWindows.good,t.Timing.Micro(i-1)),e.scoreWindows.bad=wi(n.ts)),Si(e.scoreWindows.bad,i)),e.scoreWindows.bad?Si(e.scoreWindows.bad,e.clusterWindow.max):e.scoreWindows.needsImprovement?Si(e.scoreWindows.needsImprovement,e.clusterWindow.max):Si(e.scoreWindows.good,e.clusterWindow.max);const a=n.args.data?.weighted_score_delta;void 0!==a&&a>c&&(c=a,l=n)}l&&(e.worstShiftEvent=l),e.ts=e.events[0].ts;const d=n.Timing.eventTimingsMicroSeconds(e.events[e.events.length-1]);if(e.dur=t.Timing.Micro(d.endTime-e.events[0].ts+ri),s>Ei&&(Ti=o,Ei=s),e.navigationId){i.MapUtilities.getWithDefault(Mi,e.navigationId,(()=>[])).push(e)}}}(),function(){const{traceBounds:e}=ge();Ii.push({ts:e.min,score:0});for(const e of yi){let t=0;e.events[0].args.data&&Ii.push({ts:e.clusterWindow.min,score:e.events[0].args.data.weighted_score_delta});for(let n=0;n<e.events.length;n++){const i=e.events[n];i.args.data&&(t+=i.args.data.weighted_score_delta,Ii.push({ts:i.ts,score:t}))}Ii.push({ts:e.clusterWindow.max,score:0})}}(),function(){pi.clear();for(const e of oi)if(e.args.data?.impacted_nodes)for(const t of e.args.data.impacted_nodes)pi.add(t.node_id);for(const e of ci)e.args.data?.nodeId&&pi.add(e.args.data.nodeId);for(const e of li)e.args.data?.nodeId&&pi.add(e.args.data.nodeId)}()},handleEvent:function(e){if(!t.Events.isLayoutShift(e)||e.args.data?.had_recent_input)if(t.Events.isLayoutInvalidationTracking(e))ci.push(e);else if(t.Events.isScheduleStyleInvalidationTracking(e)&&li.push(e),t.Events.isStyleRecalcInvalidationTracking(e)&&di.push(e),t.Events.isPrePaint(e))hi.push(e);else{if(t.Events.isRenderFrameImplCreateChildFrame(e)&&ui.push(e),t.Events.isDomLoading(e)&&fi.push(e),t.Events.isLayoutImageUnsized(e)&&mi.push(e),t.Events.isBeginRemoteFontLoad(e)&&gi.push({display:e.args.display,url:e.args.url,beginRemoteFontLoadEvent:e}),t.Events.isRemoteFontLoaded(e))for(const t of gi)t.url===e.args.url&&(t.name=e.args.name);t.Events.isPaintImage(e)&&vi.push(e)}else oi.push(e)},reset:function(){oi.length=0,ci.length=0,li.length=0,di.length=0,hi.length=0,vi.length=0,ui.length=0,mi.length=0,fi.length=0,gi.length=0,pi.clear(),yi.length=0,Ei=0,Ii.length=0,Ti=-1,Mi.clear()},scoreClassificationForLayoutShift:function(e){let t="good";return e>=.1&&(t="ok"),e>=.25&&(t="bad"),t}});const _i=new Map;var Ri=Object.freeze({__proto__:null,data:function(){return{updateCountersByProcess:_i}},finalize:async function(){},handleEvent:function(e){if(t.Events.isUpdateCounters(e)){const t=i.MapUtilities.getWithDefault(_i,e.pid,(()=>[]));t.push(e),_i.set(e.pid,t)}},reset:function(){_i.clear()}});const Ci=new Map;var Li=Object.freeze({__proto__:null,data:function(){return{frames:Ci}},finalize:async function(){},handleEvent:function(e){if(t.Events.isTracingStartedInBrowser(e))for(const t of e.args.data?.frames??[])Ci.set(t.frame,t);else if(t.Events.isCommitLoad(e)){const t=e.args.data;if(!t)return;const n=Ci.get(t.frame);if(!n)return;Ci.set(t.frame,{...n,url:t.url||n.url,name:t.name||t.name})}},reset:function(){Ci.clear()}});const ki=new Map;function bi(e,t){for(const n of e.frameByProcessId?.values()){const e=n.get(t);if(e)return e}return null}function Di(e,t){return e.find((e=>e.args.data.url===t.url))??null}function Ni(e){if(!e.sourceMap)throw new Error("expected source map");const t=e.sourceMap,n=e.content??"",i=n.length,s=n.split("\n"),a={},r=i;let o=r;const c=function(e){const t=new Map,n=e.mappings();for(let e=0;e<n.length-1;e++){const i=n[e],s=n[e+1];i.lineNumber===s.lineNumber&&t.set(i,s.columnNumber)}return t}(e.sourceMap);for(const e of t.mappings()){const n=e.sourceURL,i=e.lineNumber,r=e.columnNumber,l=c.get(e);if(!n)continue;const d=s[i];if(null==d){return{errorMessage:`${t.url()} mapping for line out of bounds: ${i+1}`}}if(r>d.length){return{errorMessage:`${t.url()} mapping for column out of bounds: ${i+1}:${r}`}}let u=0;if(void 0!==l){if(l>d.length){return{errorMessage:`${t.url()} mapping for last column out of bounds: ${i+1}:${l}`}}u=l-r}else u=d.length-r+1;a[n]=(a[n]||0)+u,o-=u}return{files:a,unmappedBytes:o,totalBytes:r}}function Bi(e,t){if(!e)return;const n=e.startsWith("data:");if(!t.isFreshRecording&&t.metadata?.sourceMaps&&!n){const n=t.metadata.sourceMaps.find((t=>t.sourceMapUrl===e));if(n)return n.sourceMap}}var Wi=Object.freeze({__proto__:null,data:function(){return{scripts:[...ki.values()]}},deps:function(){return["Meta","NetworkRequests"]},finalize:async function(e){const t=[...Re().byId.values()];for(const e of ki.values())e.request=Di(t,e)??void 0;if(!e.resolveSourceMap)return;const n=ge(),i=[];for(const t of ki.values()){if(!t.frame||!t.url||!t.sourceMapUrl)continue;const s=bi(n,t.frame)?.url;if(!s)continue;let a=t.url;t.sourceUrl&&(a=r.ParsedURL.ParsedURL.completeURL(s,t.sourceUrl)??t.sourceUrl);const o=r.ParsedURL.ParsedURL.completeURL(a,t.sourceMapUrl);if(!o)continue;t.sourceMapUrl=o;const c={scriptId:t.scriptId,scriptUrl:a,sourceMapUrl:o,frame:t.frame,cachedRawSourceMap:Bi(o,e)},l=e.resolveSourceMap(c).then((e=>{e&&(t.sourceMap=e)}));i.push(l)}await Promise.all(i)},getScriptGeneratedSizes:function(e){return e.sourceMap&&!e.sizes&&(e.sizes=Ni(e)),e.sizes??null},handleEvent:function(e){const n=(e,t)=>{const n=String(t),s=`${e}.${n}`;return i.MapUtilities.getWithDefault(ki,s,(()=>({isolate:e,scriptId:n,frame:"",ts:0})))};if(t.Events.isTargetRundownEvent(e)&&e.args.data){const{isolate:t,scriptId:i,frame:s}=e.args.data,a=n(t,i);return a.frame=s,void(a.ts=e.ts)}if(t.Events.isV8SourceRundownEvent(e)){const{isolate:t,scriptId:i,url:s,sourceUrl:a,sourceMapUrl:r,startLine:o,startColumn:c}=e.args.data,l=n(t,i);return l.url=s,a&&(l.sourceUrl=a),r&&(l.sourceMapUrl=r),void(l.inline=Boolean(o||c))}if(t.Events.isV8SourceRundownSourcesScriptCatchupEvent(e)){const{isolate:t,scriptId:i,sourceText:s}=e.args.data;n(t,i).content=s}else if(t.Events.isV8SourceRundownSourcesLargeScriptCatchupEvent(e)){const{isolate:t,scriptId:i,sourceText:s}=e.args.data,a=n(t,i);a.content=(a.content??"")+s}else;},reset:function(){ki.clear()}});let Ui=null;const Oi=new Map;var xi=Object.freeze({__proto__:null,data:function(){return{dataForUpdateLayoutEvent:Oi}},finalize:async function(){},handleEvent:function(e){t.Events.isSelectorStats(e)&&Ui&&e.args.selector_stats?Oi.set(Ui,{timings:e.args.selector_stats.selector_timings}):t.Events.isUpdateLayoutTree(e)&&(Ui=e)},reset:function(){Ui=null,Oi.clear()}});const zi=[],Ai=[],qi=[],ji=n.Timing.milliToMicro(t.Timing.Milli(200)),Hi=ji,$i=n.Timing.milliToMicro(t.Timing.Milli(500));let Gi=null;const Vi=[],Xi=[],Yi=new Map,Ki=[];const Qi=new Set(["pointerdown","touchstart","pointerup","touchend","mousedown","mouseup","click"]),Ji=new Set(["keydown","keypress","keyup"]);function Zi(e){return Qi.has(e.type)?"POINTER":Ji.has(e.type)?"KEYBOARD":"OTHER"}function es(e){const n={POINTER:new Map,KEYBOARD:new Map,OTHER:new Map};function i(e){const i=Zi(e),s=n[i],a=t.Timing.Micro(e.ts+e.dur),r=s.get(a);if(r){if(e.ts<r.ts)s.set(a,e);else if(e.ts===r.ts&&e.interactionId===r.interactionId){const t=r.processingEnd-r.processingStart;e.processingEnd-e.processingStart>t&&s.set(a,e)}e.processingStart<r.processingStart&&(r.processingStart=e.processingStart,ts(r)),e.processingEnd>r.processingEnd&&(r.processingEnd=e.processingEnd,ts(r))}else s.set(a,e)}for(const t of e)i(t);const s=Object.values(n).flatMap((e=>Array.from(e.values())));return s.sort(((e,t)=>e.ts-t.ts)),s}function ts(e){const n=e.args.data.beginEvent,i=e.args.data.endEvent;e.inputDelay=t.Timing.Micro(e.processingStart-n.ts),e.mainThreadHandling=t.Timing.Micro(e.processingEnd-e.processingStart),e.presentationDelay=t.Timing.Micro(i.ts-e.processingEnd)}function ns(){return{allEvents:zi,beginCommitCompositorFrameEvents:Ai,parseMetaViewportEvents:qi,interactionEvents:Vi,interactionEventsWithNoNesting:Xi,longestInteractionEvent:Gi,interactionsOverThreshold:new Set(Vi.filter((e=>e.dur>ji)))}}var is=Object.freeze({__proto__:null,LONG_INTERACTION_THRESHOLD:ji,categoryOfInteraction:Zi,data:ns,deps:function(){return["Meta"]},finalize:async function(){const{navigationsByFrameId:e}=ge();for(const i of Ki){const s=Yi.get(i.id);if(!s)continue;const{type:a,interactionId:r,timeStamp:o,processingStart:c,processingEnd:l}=i.args.data;if(!(a&&r&&o&&c&&l))continue;const d=t.Timing.Micro(n.Timing.milliToMicro(c)-n.Timing.milliToMicro(o)+i.ts),u=t.Timing.Micro(n.Timing.milliToMicro(l)-n.Timing.milliToMicro(o)+i.ts),f=i.args.frame??i.args.data.frame??"",m=n.Trace.getNavigationForTraceEvent(i,f,e),g=m?.args.data?.navigationId,p=n.SyntheticEvents.SyntheticEventsManager.registerSyntheticEvent({rawSourceEvent:i,cat:i.cat,name:i.name,pid:i.pid,tid:i.tid,ph:i.ph,processingStart:d,processingEnd:u,inputDelay:t.Timing.Micro(-1),mainThreadHandling:t.Timing.Micro(-1),presentationDelay:t.Timing.Micro(-1),args:{data:{beginEvent:i,endEvent:s,frame:f,navigationId:g}},ts:i.ts,dur:t.Timing.Micro(s.ts-i.ts),type:i.args.data.type,interactionId:i.args.data.interactionId});ts(p),Vi.push(p)}Xi.push(...es(Vi));for(const e of Xi)(!Gi||Gi.dur<e.dur)&&(Gi=e)},handleEvent:function(e){if(t.Events.isBeginCommitCompositorFrame(e))return void Ai.push(e);if(t.Events.isParseMetaViewport(e))return void qi.push(e);if(!t.Events.isEventTiming(e))return;if(t.Events.isEventTimingEnd(e)&&Yi.set(e.id,e),zi.push(e),!e.args.data||!t.Events.isEventTimingStart(e))return;const{duration:n,interactionId:i}=e.args.data;n<1||void 0===i||0===i||Ki.push(e)},removeNestedInteractions:es,reset:function(){zi.length=0,Ai.length=0,qi.length=0,Vi.length=0,Ki.length=0,Yi.clear(),Xi.length=0,Gi=null},scoreClassificationForInteractionToNextPaint:function(e){return e<=Hi?"good":e<=$i?"ok":"bad"}});const ss=new Map,as=new Map,rs=[],os=[],cs=[],ls=n.Timing.milliToMicro(t.Timing.Milli(30)),ds=n.Timing.milliToMicro(t.Timing.Milli(50));function us(e,t){const n=i.MapUtilities.getWithDefault(ss,e,(()=>[]));n.push(t),ss.set(e,n);const s=i.MapUtilities.getWithDefault(as,t,(()=>[]));s.push(e),as.set(t,s)}function fs(e,t,n=!0){let i=t.at(-1);for(;i&&e.ts>i.ts+(i.dur||0);)t.pop(),i=t.at(-1);n&&t.push(e)}var ms=Object.freeze({__proto__:null,FORCED_REFLOW_THRESHOLD:ls,LONG_MAIN_THREAD_TASK_THRESHOLD:ds,data:function(){return{perEvent:ss,perWarning:as}},deps:function(){return["UserInteractions"]},finalize:async function(){const e=ns().interactionsOverThreshold;for(const t of e)us(t,"LONG_INTERACTION")},handleEvent:function(e){if(function(e){if(fs(e,rs),fs(e,os,t.Events.isJSInvocationEvent(e)),os.length&&("Layout"===e.name||"UpdateLayoutTree"===e.name))return void cs.push(e);if(1===rs.length){const e=cs.reduce(((e,t)=>e+(t.dur||0)),0);e>=ls&&cs.forEach((e=>us(e,"FORCED_REFLOW"))),cs.length=0}}(e),"RunTask"!==e.name)if(t.Events.isFireIdleCallback(e)){const{duration:t}=n.Timing.eventTimingsMilliSeconds(e);t>e.args.data.allottedMilliseconds&&us(e,"IDLE_CALLBACK_OVER_TIME")}else;else{const{duration:t}=n.Timing.eventTimingsMicroSeconds(e);t>ds&&us(e,"LONG_TASK")}},reset:function(){ss.clear(),as.clear(),rs.length=0,os.length=0,cs.length=0}});const gs=[],ps=new Map,hs=new Map;var vs=Object.freeze({__proto__:null,data:function(){return{workerSessionIdEvents:gs,workerIdByThread:ps,workerURLById:hs}},finalize:async function(){for(const e of gs)e.args.data&&(ps.set(e.args.data.workerThreadId,e.args.data.workerId),hs.set(e.args.data.workerId,e.args.data.url))},handleEvent:function(e){t.Events.isTracingSessionIdForWorker(e)&&gs.push(e)},reset:function(){gs.length=0,ps.clear(),hs.clear()}}),Es=Object.freeze({__proto__:null,AnimationFrames:y,Animations:w,AsyncJSCalls:mt,AuctionWorklets:H,DOMStats:pt,ExtensionTraceData:Bt,Flows:B,Frames:an,GPU:cn,ImagePainting:gn,Initiators:wn,Invalidations:kn,LargestImagePaint:Yn,LargestTextPaint:Qn,LayerTree:Ht,LayoutShifts:Pi,Memory:Ri,Meta:pe,NetworkRequests:Ce,PageFrames:Li,PageLoadMetrics:Gn,Renderer:st,Samples:We,Screenshots:si,Scripts:Wi,SelectorStats:xi,UserInteractions:is,UserTimings:St,Warnings:ms,Workers:vs}),Ts=Object.freeze({__proto__:null});export{m as Helpers,Es as ModelHandlers,Xt as Threads,Ts as Types};
