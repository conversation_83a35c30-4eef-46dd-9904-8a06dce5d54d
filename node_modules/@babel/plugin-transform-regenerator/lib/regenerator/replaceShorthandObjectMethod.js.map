{"version": 3, "names": ["_core", "require", "replaceShorthandObjectMethod", "path", "node", "t", "isFunction", "Error", "isObjectMethod", "generator", "parameters", "params", "map", "param", "cloneNode", "functionExpression", "body", "async", "replaceWith", "objectProperty", "key", "computed", "get"], "sources": ["../../src/regenerator/replaceShorthandObjectMethod.ts"], "sourcesContent": ["import type { NodePath } from \"@babel/core\";\nimport { types as t } from \"@babel/core\";\n\n// this function converts a shorthand object generator method into a normal\n// (non-shorthand) object property which is a generator function expression. for\n// example, this:\n//\n//  var foo = {\n//    *bar(baz) { return 5; }\n//  }\n//\n// should be replaced with:\n//\n//  var foo = {\n//    bar: function*(baz) { return 5; }\n//  }\n//\n// to do this, it clones the parameter array and the body of the object generator\n// method into a new FunctionExpression.\n//\n// this method can be passed any Function AST node path, and it will return\n// either:\n//   a) the path that was passed in (iff the path did not need to be replaced) or\n//   b) the path of the new FunctionExpression that was created as a replacement\n//     (iff the path did need to be replaced)\n//\n// In either case, though, the caller can count on the fact that the return value\n// is a Function AST node path.\n//\n// If this function is called with an AST node path that is not a Function (or with an\n// argument that isn't an AST node path), it will throw an error.\nexport default function replaceShorthandObjectMethod(path: NodePath): NodePath {\n  if (!path.node || !t.isFunction(path.node)) {\n    throw new Error(\n      \"replaceShorthandObjectMethod can only be called on Function AST node paths.\",\n    );\n  }\n\n  // this function only replaces shorthand object methods (called ObjectMethod\n  // in Babel-speak).\n  if (!t.isObjectMethod(path.node)) {\n    return path;\n  }\n\n  // this function only replaces generators.\n  if (!path.node.generator) {\n    return path;\n  }\n\n  const parameters = path.node.params.map(function (param: any) {\n    return t.cloneNode(param);\n  });\n\n  const functionExpression = t.functionExpression(\n    null, // id\n    parameters, // params\n    t.cloneNode(path.node.body), // body\n    path.node.generator,\n    path.node.async,\n  );\n\n  path.replaceWith(\n    t.objectProperty(\n      t.cloneNode(path.node.key), // key\n      functionExpression, //value\n      path.node.computed, // computed\n      false, // shorthand\n    ),\n  );\n\n  // path now refers to the ObjectProperty AST node path, but we want to return a\n  // Function AST node path for the function expression we created. we know that\n  // the FunctionExpression we just created is the value of the ObjectProperty,\n  // so return the \"value\" path off of this path.\n  return path.get(\"value\");\n}\n"], "mappings": ";;;;;;AACA,IAAAA,KAAA,GAAAC,OAAA;AA8Be,SAASC,4BAA4BA,CAACC,IAAc,EAAY;EAC7E,IAAI,CAACA,IAAI,CAACC,IAAI,IAAI,CAACC,WAAC,CAACC,UAAU,CAACH,IAAI,CAACC,IAAI,CAAC,EAAE;IAC1C,MAAM,IAAIG,KAAK,CACb,6EACF,CAAC;EACH;EAIA,IAAI,CAACF,WAAC,CAACG,cAAc,CAACL,IAAI,CAACC,IAAI,CAAC,EAAE;IAChC,OAAOD,IAAI;EACb;EAGA,IAAI,CAACA,IAAI,CAACC,IAAI,CAACK,SAAS,EAAE;IACxB,OAAON,IAAI;EACb;EAEA,MAAMO,UAAU,GAAGP,IAAI,CAACC,IAAI,CAACO,MAAM,CAACC,GAAG,CAAC,UAAUC,KAAU,EAAE;IAC5D,OAAOR,WAAC,CAACS,SAAS,CAACD,KAAK,CAAC;EAC3B,CAAC,CAAC;EAEF,MAAME,kBAAkB,GAAGV,WAAC,CAACU,kBAAkB,CAC7C,IAAI,EACJL,UAAU,EACVL,WAAC,CAACS,SAAS,CAACX,IAAI,CAACC,IAAI,CAACY,IAAI,CAAC,EAC3Bb,IAAI,CAACC,IAAI,CAACK,SAAS,EACnBN,IAAI,CAACC,IAAI,CAACa,KACZ,CAAC;EAEDd,IAAI,CAACe,WAAW,CACdb,WAAC,CAACc,cAAc,CACdd,WAAC,CAACS,SAAS,CAACX,IAAI,CAACC,IAAI,CAACgB,GAAG,CAAC,EAC1BL,kBAAkB,EAClBZ,IAAI,CAACC,IAAI,CAACiB,QAAQ,EAClB,KACF,CACF,CAAC;EAMD,OAAOlB,IAAI,CAACmB,GAAG,CAAC,OAAO,CAAC;AAC1B", "ignoreList": []}