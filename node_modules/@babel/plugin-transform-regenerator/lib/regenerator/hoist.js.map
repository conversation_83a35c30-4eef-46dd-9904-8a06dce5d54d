{"version": 3, "names": ["_core", "require", "hoist", "funPath", "t", "assertFunction", "node", "vars", "__proto__", "varDeclToExpr", "vdec", "scope", "includeIdentifiers", "assertVariableDeclaration", "exprs", "declarations", "for<PERSON>ach", "dec", "id", "name", "identifier", "removeBinding", "init", "push", "assignmentExpression", "length", "sequenceExpression", "get", "traverse", "VariableDeclaration", "exit", "path", "expr", "remove", "replaceWith", "expressionStatement", "skip", "ForStatement", "isVariableDeclaration", "ForXStatement", "left", "FunctionDeclaration", "assignment", "cloneNode", "functionExpression", "generateUidIdentifierBasedOnNode", "params", "body", "generator", "async", "parentPath", "isBlockStatement", "unshiftContainer", "FunctionExpression", "ArrowFunctionExpression", "paramNames", "<PERSON><PERSON><PERSON><PERSON>", "param", "isIdentifier", "Object", "keys", "hasOwnProperty", "call", "variableDeclarator"], "sources": ["../../src/regenerator/hoist.ts"], "sourcesContent": ["import type { NodePath } from \"@babel/core\";\nimport { types as t } from \"@babel/core\";\n\n// The hoist function takes a FunctionExpression or FunctionDeclaration\n// and replaces any Declaration nodes in its body with assignments, then\n// returns a VariableDeclaration containing just the names of the removed\n// declarations.\nexport function hoist(\n  funPath: NodePath<t.FunctionExpression | t.FunctionDeclaration>,\n) {\n  t.assertFunction(funPath.node);\n\n  const vars: Record<string, t.Identifier> = { __proto__: null };\n\n  function varDeclToExpr(\n    { node: vdec, scope }: NodePath,\n    includeIdentifiers: boolean,\n  ) {\n    t.assertVariableDeclaration(vdec);\n    // TODO assert.equal(vdec.kind, \"var\");\n    const exprs: t.Expression[] = [];\n\n    vdec.declarations.forEach(function (\n      dec: t.VariableDeclarator & { id: t.Identifier },\n    ) {\n      // Note: We duplicate 'dec.id' here to ensure that the variable declaration IDs don't\n      // have the same 'loc' value, since that can make sourcemaps and retainLines behave poorly.\n      vars[dec.id.name] = t.identifier(dec.id.name);\n\n      // Remove the binding, to avoid \"duplicate declaration\" errors when it will\n      // be injected again.\n      scope.removeBinding(dec.id.name);\n\n      if (dec.init) {\n        exprs.push(t.assignmentExpression(\"=\", dec.id, dec.init));\n      } else if (includeIdentifiers) {\n        exprs.push(dec.id);\n      }\n    });\n\n    if (exprs.length === 0) return null;\n\n    if (exprs.length === 1) return exprs[0];\n\n    return t.sequenceExpression(exprs);\n  }\n\n  funPath.get(\"body\").traverse({\n    VariableDeclaration: {\n      exit: function (path) {\n        const expr = varDeclToExpr(path, false);\n        if (expr === null) {\n          path.remove();\n        } else {\n          // We don't need to traverse this expression any further because\n          // there can't be any new declarations inside an expression.\n          path.replaceWith(t.expressionStatement(expr));\n        }\n\n        // Since the original node has been either removed or replaced,\n        // avoid traversing it any further.\n        path.skip();\n      },\n    },\n\n    ForStatement: function (path) {\n      const init = path.get(\"init\");\n      if (init.isVariableDeclaration()) {\n        const expr = varDeclToExpr(init, false);\n        if (expr) {\n          init.replaceWith(expr);\n        } else {\n          init.remove();\n        }\n      }\n    },\n\n    ForXStatement: function (path) {\n      const left = path.get(\"left\");\n      if (left.isVariableDeclaration()) {\n        left.replaceWith(varDeclToExpr(left, true));\n      }\n    },\n\n    FunctionDeclaration: function (path) {\n      const node = path.node;\n      vars[node.id.name] = node.id;\n\n      const assignment = t.expressionStatement(\n        t.assignmentExpression(\n          \"=\",\n          t.cloneNode(node.id),\n          t.functionExpression(\n            path.scope.generateUidIdentifierBasedOnNode(node),\n            node.params,\n            node.body,\n            node.generator,\n            node.async,\n          ),\n        ),\n      );\n\n      if (path.parentPath.isBlockStatement()) {\n        // Insert the assignment form before the first statement in the\n        // enclosing block.\n        path.parentPath.unshiftContainer(\"body\", assignment);\n\n        // Remove the function declaration now that we've inserted the\n        // equivalent assignment form at the beginning of the block.\n        path.remove();\n      } else {\n        // If the parent node is not a block statement, then we can just\n        // replace the declaration with the equivalent assignment form\n        // without worrying about hoisting it.\n        path.replaceWith(assignment);\n      }\n\n      // Remove the binding, to avoid \"duplicate declaration\" errors when it will\n      // be injected again.\n      path.scope.removeBinding(node.id.name);\n\n      // Don't hoist variables out of inner functions.\n      path.skip();\n    },\n\n    FunctionExpression: function (path) {\n      // Don't descend into nested function expressions.\n      path.skip();\n    },\n\n    ArrowFunctionExpression: function (path) {\n      // Don't descend into nested function expressions.\n      path.skip();\n    },\n  });\n\n  const paramNames: Record<string, t.Identifier> = { __proto__: null };\n  funPath.get(\"params\").forEach(function (paramPath) {\n    const param = paramPath.node;\n    if (t.isIdentifier(param)) {\n      paramNames[param.name] = param;\n    } else {\n      // Variables declared by destructuring parameter patterns will be\n      // harmlessly re-declared.\n    }\n  });\n\n  const declarations: t.VariableDeclarator[] = [];\n\n  Object.keys(vars).forEach(function (name) {\n    if (!Object.hasOwn(paramNames, name)) {\n      declarations.push(t.variableDeclarator(vars[name], null));\n    }\n  });\n\n  return declarations;\n}\n"], "mappings": ";;;;;;AACA,IAAAA,KAAA,GAAAC,OAAA;AAMO,SAASC,KAAKA,CACnBC,OAA+D,EAC/D;EACAC,WAAC,CAACC,cAAc,CAACF,OAAO,CAACG,IAAI,CAAC;EAE9B,MAAMC,IAAkC,GAAG;IAAEC,SAAS,EAAE;EAAK,CAAC;EAE9D,SAASC,aAAaA,CACpB;IAAEH,IAAI,EAAEI,IAAI;IAAEC;EAAgB,CAAC,EAC/BC,kBAA2B,EAC3B;IACAR,WAAC,CAACS,yBAAyB,CAACH,IAAI,CAAC;IAEjC,MAAMI,KAAqB,GAAG,EAAE;IAEhCJ,IAAI,CAACK,YAAY,CAACC,OAAO,CAAC,UACxBC,GAAgD,EAChD;MAGAV,IAAI,CAACU,GAAG,CAACC,EAAE,CAACC,IAAI,CAAC,GAAGf,WAAC,CAACgB,UAAU,CAACH,GAAG,CAACC,EAAE,CAACC,IAAI,CAAC;MAI7CR,KAAK,CAACU,aAAa,CAACJ,GAAG,CAACC,EAAE,CAACC,IAAI,CAAC;MAEhC,IAAIF,GAAG,CAACK,IAAI,EAAE;QACZR,KAAK,CAACS,IAAI,CAACnB,WAAC,CAACoB,oBAAoB,CAAC,GAAG,EAAEP,GAAG,CAACC,EAAE,EAAED,GAAG,CAACK,IAAI,CAAC,CAAC;MAC3D,CAAC,MAAM,IAAIV,kBAAkB,EAAE;QAC7BE,KAAK,CAACS,IAAI,CAACN,GAAG,CAACC,EAAE,CAAC;MACpB;IACF,CAAC,CAAC;IAEF,IAAIJ,KAAK,CAACW,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAEnC,IAAIX,KAAK,CAACW,MAAM,KAAK,CAAC,EAAE,OAAOX,KAAK,CAAC,CAAC,CAAC;IAEvC,OAAOV,WAAC,CAACsB,kBAAkB,CAACZ,KAAK,CAAC;EACpC;EAEAX,OAAO,CAACwB,GAAG,CAAC,MAAM,CAAC,CAACC,QAAQ,CAAC;IAC3BC,mBAAmB,EAAE;MACnBC,IAAI,EAAE,SAAAA,CAAUC,IAAI,EAAE;QACpB,MAAMC,IAAI,GAAGvB,aAAa,CAACsB,IAAI,EAAE,KAAK,CAAC;QACvC,IAAIC,IAAI,KAAK,IAAI,EAAE;UACjBD,IAAI,CAACE,MAAM,CAAC,CAAC;QACf,CAAC,MAAM;UAGLF,IAAI,CAACG,WAAW,CAAC9B,WAAC,CAAC+B,mBAAmB,CAACH,IAAI,CAAC,CAAC;QAC/C;QAIAD,IAAI,CAACK,IAAI,CAAC,CAAC;MACb;IACF,CAAC;IAEDC,YAAY,EAAE,SAAAA,CAAUN,IAAI,EAAE;MAC5B,MAAMT,IAAI,GAAGS,IAAI,CAACJ,GAAG,CAAC,MAAM,CAAC;MAC7B,IAAIL,IAAI,CAACgB,qBAAqB,CAAC,CAAC,EAAE;QAChC,MAAMN,IAAI,GAAGvB,aAAa,CAACa,IAAI,EAAE,KAAK,CAAC;QACvC,IAAIU,IAAI,EAAE;UACRV,IAAI,CAACY,WAAW,CAACF,IAAI,CAAC;QACxB,CAAC,MAAM;UACLV,IAAI,CAACW,MAAM,CAAC,CAAC;QACf;MACF;IACF,CAAC;IAEDM,aAAa,EAAE,SAAAA,CAAUR,IAAI,EAAE;MAC7B,MAAMS,IAAI,GAAGT,IAAI,CAACJ,GAAG,CAAC,MAAM,CAAC;MAC7B,IAAIa,IAAI,CAACF,qBAAqB,CAAC,CAAC,EAAE;QAChCE,IAAI,CAACN,WAAW,CAACzB,aAAa,CAAC+B,IAAI,EAAE,IAAI,CAAC,CAAC;MAC7C;IACF,CAAC;IAEDC,mBAAmB,EAAE,SAAAA,CAAUV,IAAI,EAAE;MACnC,MAAMzB,IAAI,GAAGyB,IAAI,CAACzB,IAAI;MACtBC,IAAI,CAACD,IAAI,CAACY,EAAE,CAACC,IAAI,CAAC,GAAGb,IAAI,CAACY,EAAE;MAE5B,MAAMwB,UAAU,GAAGtC,WAAC,CAAC+B,mBAAmB,CACtC/B,WAAC,CAACoB,oBAAoB,CACpB,GAAG,EACHpB,WAAC,CAACuC,SAAS,CAACrC,IAAI,CAACY,EAAE,CAAC,EACpBd,WAAC,CAACwC,kBAAkB,CAClBb,IAAI,CAACpB,KAAK,CAACkC,gCAAgC,CAACvC,IAAI,CAAC,EACjDA,IAAI,CAACwC,MAAM,EACXxC,IAAI,CAACyC,IAAI,EACTzC,IAAI,CAAC0C,SAAS,EACd1C,IAAI,CAAC2C,KACP,CACF,CACF,CAAC;MAED,IAAIlB,IAAI,CAACmB,UAAU,CAACC,gBAAgB,CAAC,CAAC,EAAE;QAGtCpB,IAAI,CAACmB,UAAU,CAACE,gBAAgB,CAAC,MAAM,EAAEV,UAAU,CAAC;QAIpDX,IAAI,CAACE,MAAM,CAAC,CAAC;MACf,CAAC,MAAM;QAILF,IAAI,CAACG,WAAW,CAACQ,UAAU,CAAC;MAC9B;MAIAX,IAAI,CAACpB,KAAK,CAACU,aAAa,CAACf,IAAI,CAACY,EAAE,CAACC,IAAI,CAAC;MAGtCY,IAAI,CAACK,IAAI,CAAC,CAAC;IACb,CAAC;IAEDiB,kBAAkB,EAAE,SAAAA,CAAUtB,IAAI,EAAE;MAElCA,IAAI,CAACK,IAAI,CAAC,CAAC;IACb,CAAC;IAEDkB,uBAAuB,EAAE,SAAAA,CAAUvB,IAAI,EAAE;MAEvCA,IAAI,CAACK,IAAI,CAAC,CAAC;IACb;EACF,CAAC,CAAC;EAEF,MAAMmB,UAAwC,GAAG;IAAE/C,SAAS,EAAE;EAAK,CAAC;EACpEL,OAAO,CAACwB,GAAG,CAAC,QAAQ,CAAC,CAACX,OAAO,CAAC,UAAUwC,SAAS,EAAE;IACjD,MAAMC,KAAK,GAAGD,SAAS,CAAClD,IAAI;IAC5B,IAAIF,WAAC,CAACsD,YAAY,CAACD,KAAK,CAAC,EAAE;MACzBF,UAAU,CAACE,KAAK,CAACtC,IAAI,CAAC,GAAGsC,KAAK;IAChC,CAAC,MAAM,CAGP;EACF,CAAC,CAAC;EAEF,MAAM1C,YAAoC,GAAG,EAAE;EAE/C4C,MAAM,CAACC,IAAI,CAACrD,IAAI,CAAC,CAACS,OAAO,CAAC,UAAUG,IAAI,EAAE;IACxC,IAAI,CAAC0C,cAAA,CAAAC,IAAA,CAAcP,UAAU,EAAEpC,IAAI,CAAC,EAAE;MACpCJ,YAAY,CAACQ,IAAI,CAACnB,WAAC,CAAC2D,kBAAkB,CAACxD,IAAI,CAACY,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;IAC3D;EACF,CAAC,CAAC;EAEF,OAAOJ,YAAY;AACrB", "ignoreList": []}