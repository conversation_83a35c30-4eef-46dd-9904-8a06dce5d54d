{"version": 3, "names": ["_assert", "require", "_core", "mMap", "WeakMap", "m", "node", "has", "set", "get", "makePredicate", "propertyName", "knownTypes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "t", "assertNode", "result", "check", "child", "Array", "isArray", "some", "isNode", "assert", "strictEqual", "predicate", "keys", "VISITOR_KEYS", "type", "i", "length", "key", "meta", "hasOwnProperty", "call", "opaqueTypes", "FunctionExpression", "ArrowFunctionExpression", "sideEffectTypes", "CallExpression", "ForInStatement", "UnaryExpression", "BinaryExpression", "AssignmentExpression", "UpdateExpression", "NewExpression", "leapTypes", "YieldExpression", "BreakStatement", "ContinueStatement", "ReturnStatement", "ThrowStatement", "hasSideEffects", "exports", "containsLeap"], "sources": ["../../src/regenerator/meta.ts"], "sourcesContent": ["import assert from \"node:assert\";\nimport { types as t } from \"@babel/core\";\n\nconst mMap = new WeakMap();\nfunction m(node: t.Node) {\n  if (!mMap.has(node)) {\n    mMap.set(node, {});\n  }\n  return mMap.get(node);\n}\n\nfunction makePredicate(\n  propertyName: string,\n  knownTypes: Record<string, boolean>,\n) {\n  function onlyChildren(node: t.Node) {\n    t.assertNode(node);\n\n    // Assume no side effects until we find out otherwise.\n    let result = false;\n\n    function check(child: any) {\n      if (result) {\n        // Do nothing.\n      } else if (Array.isArray(child)) {\n        child.some(check);\n      } else if (t.isNode(child)) {\n        assert.strictEqual(result, false);\n        result = predicate(child);\n      }\n      return result;\n    }\n\n    const keys = t.VISITOR_KEYS[node.type];\n    if (keys) {\n      for (let i = 0; i < keys.length; i++) {\n        const key = keys[i];\n        const child = node[key as keyof typeof node];\n        check(child);\n      }\n    }\n\n    return result;\n  }\n\n  function predicate(node: t.Node) {\n    t.assertNode(node);\n\n    const meta = m(node);\n    if (Object.hasOwn(meta, propertyName)) return meta[propertyName];\n\n    // Certain types are \"opaque,\" which means they have no side\n    // effects or leaps and we don't care about their subexpressions.\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    if (Object.hasOwn(opaqueTypes, node.type))\n      return (meta[propertyName] = false);\n\n    if (Object.hasOwn(knownTypes, node.type))\n      return (meta[propertyName] = true);\n\n    return (meta[propertyName] = onlyChildren(node));\n  }\n\n  predicate.onlyChildren = onlyChildren;\n\n  return predicate;\n}\n\nconst opaqueTypes = {\n  FunctionExpression: true,\n  ArrowFunctionExpression: true,\n};\n\n// These types potentially have side effects regardless of what side\n// effects their subexpressions have.\nconst sideEffectTypes = {\n  CallExpression: true, // Anything could happen!\n  ForInStatement: true, // Modifies the key variable.\n  UnaryExpression: true, // Think delete.\n  BinaryExpression: true, // Might invoke .toString() or .valueOf().\n  AssignmentExpression: true, // Side-effecting by definition.\n  UpdateExpression: true, // Updates are essentially assignments.\n  NewExpression: true, // Similar to CallExpression.\n};\n\n// These types are the direct cause of all leaps in control flow.\nconst leapTypes = {\n  YieldExpression: true,\n  BreakStatement: true,\n  ContinueStatement: true,\n  ReturnStatement: true,\n  ThrowStatement: true,\n};\n\n// All leap types are also side effect types.\nfor (const type in leapTypes) {\n  if (Object.hasOwn(leapTypes, type)) {\n    sideEffectTypes[type as keyof typeof sideEffectTypes] =\n      leapTypes[type as keyof typeof leapTypes];\n  }\n}\n\nexport const hasSideEffects = makePredicate(\"hasSideEffects\", sideEffectTypes);\nexport const containsLeap = makePredicate(\"containsLeap\", leapTypes);\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAEA,MAAME,IAAI,GAAG,IAAIC,OAAO,CAAC,CAAC;AAC1B,SAASC,CAACA,CAACC,IAAY,EAAE;EACvB,IAAI,CAACH,IAAI,CAACI,GAAG,CAACD,IAAI,CAAC,EAAE;IACnBH,IAAI,CAACK,GAAG,CAACF,IAAI,EAAE,CAAC,CAAC,CAAC;EACpB;EACA,OAAOH,IAAI,CAACM,GAAG,CAACH,IAAI,CAAC;AACvB;AAEA,SAASI,aAAaA,CACpBC,YAAoB,EACpBC,UAAmC,EACnC;EACA,SAASC,YAAYA,CAACP,IAAY,EAAE;IAClCQ,WAAC,CAACC,UAAU,CAACT,IAAI,CAAC;IAGlB,IAAIU,MAAM,GAAG,KAAK;IAElB,SAASC,KAAKA,CAACC,KAAU,EAAE;MACzB,IAAIF,MAAM,EAAE,CAEZ,CAAC,MAAM,IAAIG,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;QAC/BA,KAAK,CAACG,IAAI,CAACJ,KAAK,CAAC;MACnB,CAAC,MAAM,IAAIH,WAAC,CAACQ,MAAM,CAACJ,KAAK,CAAC,EAAE;QAC1BK,OAAM,CAACC,WAAW,CAACR,MAAM,EAAE,KAAK,CAAC;QACjCA,MAAM,GAAGS,SAAS,CAACP,KAAK,CAAC;MAC3B;MACA,OAAOF,MAAM;IACf;IAEA,MAAMU,IAAI,GAAGZ,WAAC,CAACa,YAAY,CAACrB,IAAI,CAACsB,IAAI,CAAC;IACtC,IAAIF,IAAI,EAAE;MACR,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QACpC,MAAME,GAAG,GAAGL,IAAI,CAACG,CAAC,CAAC;QACnB,MAAMX,KAAK,GAAGZ,IAAI,CAACyB,GAAG,CAAsB;QAC5Cd,KAAK,CAACC,KAAK,CAAC;MACd;IACF;IAEA,OAAOF,MAAM;EACf;EAEA,SAASS,SAASA,CAACnB,IAAY,EAAE;IAC/BQ,WAAC,CAACC,UAAU,CAACT,IAAI,CAAC;IAElB,MAAM0B,IAAI,GAAG3B,CAAC,CAACC,IAAI,CAAC;IACpB,IAAI2B,cAAA,CAAAC,IAAA,CAAcF,IAAI,EAAErB,YAAY,CAAC,EAAE,OAAOqB,IAAI,CAACrB,YAAY,CAAC;IAKhE,IAAIsB,cAAA,CAAAC,IAAA,CAAcC,WAAW,EAAE7B,IAAI,CAACsB,IAAI,CAAC,EACvC,OAAQI,IAAI,CAACrB,YAAY,CAAC,GAAG,KAAK;IAEpC,IAAIsB,cAAA,CAAAC,IAAA,CAActB,UAAU,EAAEN,IAAI,CAACsB,IAAI,CAAC,EACtC,OAAQI,IAAI,CAACrB,YAAY,CAAC,GAAG,IAAI;IAEnC,OAAQqB,IAAI,CAACrB,YAAY,CAAC,GAAGE,YAAY,CAACP,IAAI,CAAC;EACjD;EAEAmB,SAAS,CAACZ,YAAY,GAAGA,YAAY;EAErC,OAAOY,SAAS;AAClB;AAEA,MAAMU,WAAW,GAAG;EAClBC,kBAAkB,EAAE,IAAI;EACxBC,uBAAuB,EAAE;AAC3B,CAAC;AAID,MAAMC,eAAe,GAAG;EACtBC,cAAc,EAAE,IAAI;EACpBC,cAAc,EAAE,IAAI;EACpBC,eAAe,EAAE,IAAI;EACrBC,gBAAgB,EAAE,IAAI;EACtBC,oBAAoB,EAAE,IAAI;EAC1BC,gBAAgB,EAAE,IAAI;EACtBC,aAAa,EAAE;AACjB,CAAC;AAGD,MAAMC,SAAS,GAAG;EAChBC,eAAe,EAAE,IAAI;EACrBC,cAAc,EAAE,IAAI;EACpBC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE,IAAI;EACrBC,cAAc,EAAE;AAClB,CAAC;AAGD,KAAK,MAAMvB,IAAI,IAAIkB,SAAS,EAAE;EAC5B,IAAIb,cAAA,CAAAC,IAAA,CAAcY,SAAS,EAAElB,IAAI,CAAC,EAAE;IAClCU,eAAe,CAACV,IAAI,CAAiC,GACnDkB,SAAS,CAAClB,IAAI,CAA2B;EAC7C;AACF;AAEO,MAAMwB,cAAc,GAAAC,OAAA,CAAAD,cAAA,GAAG1C,aAAa,CAAC,gBAAgB,EAAE4B,eAAe,CAAC;AACvE,MAAMgB,YAAY,GAAAD,OAAA,CAAAC,YAAA,GAAG5C,aAAa,CAAC,cAAc,EAAEoC,SAAS,CAAC", "ignoreList": []}