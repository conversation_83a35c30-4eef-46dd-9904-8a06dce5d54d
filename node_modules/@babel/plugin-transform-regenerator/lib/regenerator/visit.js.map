{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "getVisitor", "_assert", "require", "_hoist", "_emit", "_replaceShorthandObjectMethod", "util", "_core", "Method", "path", "state", "node", "shouldRegenerate", "container", "t", "functionExpression", "cloneNode", "body", "generator", "async", "get", "set", "returnStatement", "callExpression", "unwrapFunctionEnvironment", "Function", "exit", "replaceShorthandObjectMethod", "contextId", "scope", "generateUidIdentifier", "argsId", "ensureBlock", "body<PERSON>lockPath", "traverse", "await<PERSON><PERSON>tor", "functionSentVisitor", "context", "pluginPass", "outerBody", "innerBody", "for<PERSON>ach", "child<PERSON><PERSON>", "isExpressionStatement", "isStringLiteral", "expression", "push", "_blockHoist", "length", "outerFnExpr", "getOuterFnExpr", "assertIdentifier", "id", "vars", "hoist", "usesThis", "usesArguments", "getArgsId", "argumentsThisVisitor", "variableDeclarator", "identifier", "emitter", "Emitter", "explode", "variableDeclaration", "wrapArgs", "getContextFunction", "tryLocsList", "getTryLocsList", "nullLiteral", "thisExpression", "currentScope", "hasOwnBinding", "rename", "parent", "wrapCall", "newHelpersAvailable", "memberExpression", "addHelper", "runtimeProperty", "blockStatement", "p", "registerDeclaration", "oldDirectives", "directives", "wasGeneratorFunction", "isExpression", "replaceWith", "addComment", "insertedLocs", "getInsertedLocs", "NumericLiteral", "has", "numericLiteral", "requeue", "opts", "asyncGenerators", "generators", "funPath", "assertFunction", "isFunctionDeclaration", "getMarkedFunctionId", "markInfo", "WeakMap", "getMarkInfo", "blockPath", "findParent", "isProgram", "isBlockStatement", "block", "assert", "ok", "Array", "isArray", "info", "decl", "unshiftContainer", "decl<PERSON>ath", "strictEqual", "markedId", "markCallExp", "index", "declarations", "markCallExpPath", "FunctionExpression|FunctionDeclaration|Method", "skip", "Identifier", "name", "isReference", "ThisExpression", "MetaProperty", "meta", "property", "AwaitExpression", "argument", "helper", "yieldExpression"], "sources": ["../../src/regenerator/visit.ts"], "sourcesContent": ["\"use strict\";\n\nimport assert from \"node:assert\";\nimport { hoist } from \"./hoist.ts\";\nimport { Emitter } from \"./emit.ts\";\nimport replaceShorthandObjectMethod from \"./replaceShorthandObjectMethod.ts\";\nimport * as util from \"./util.ts\";\nimport type { NodePath, PluginPass, Visitor } from \"@babel/core\";\nimport { types as t } from \"@babel/core\";\n\nexport const getVisitor = (): Visitor<PluginPass> => ({\n  Method(path, state) {\n    const node = path.node;\n\n    if (!shouldRegenerate(node, state)) return;\n\n    const container = t.functionExpression(\n      null,\n      [],\n      t.cloneNode(node.body, false),\n      node.generator,\n      node.async,\n    );\n\n    path\n      .get(\"body\")\n      .set(\"body\", [t.returnStatement(t.callExpression(container, []))]);\n\n    // Regardless of whether or not the wrapped function is a an async method\n    // or generator the outer function should not be\n    node.async = false;\n    node.generator = false;\n\n    // Unwrap the wrapper IIFE's environment so super and this and such still work.\n    (\n      path.get(\"body.body.0.argument.callee\") as NodePath\n    ).unwrapFunctionEnvironment();\n  },\n  Function: {\n    exit(\n      path: NodePath<Exclude<t.Function, t.ArrowFunctionExpression>>,\n      state,\n    ) {\n      let node = path.node;\n\n      if (!shouldRegenerate(node, state)) return;\n\n      // if this is an ObjectMethod, we need to convert it to an ObjectProperty\n      path = replaceShorthandObjectMethod(path) as any;\n      node = path.node;\n\n      const contextId = path.scope.generateUidIdentifier(\"context\");\n      const argsId = path.scope.generateUidIdentifier(\"args\");\n\n      path.ensureBlock();\n      const bodyBlockPath = path.get(\"body\");\n\n      if (node.async) {\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        bodyBlockPath.traverse(awaitVisitor, this);\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      bodyBlockPath.traverse(functionSentVisitor, {\n        context: contextId,\n        pluginPass: this,\n      });\n\n      const outerBody: any[] = [];\n      const innerBody: any[] = [];\n\n      bodyBlockPath.get(\"body\").forEach(function (childPath: any) {\n        const node = childPath.node;\n        if (\n          t.isExpressionStatement(node) &&\n          t.isStringLiteral(node.expression)\n        ) {\n          // Babylon represents directives like \"use strict\" as elements\n          // of a bodyBlockPath.node.directives array, but they could just\n          // as easily be represented (by other parsers) as traditional\n          // string-literal-valued expression statements, so we need to\n          // handle that here. (#248)\n          outerBody.push(node);\n        } else if (node?._blockHoist != null) {\n          outerBody.push(node);\n        } else {\n          innerBody.push(node);\n        }\n      });\n\n      if (outerBody.length > 0) {\n        // Only replace the inner body if we actually hoisted any statements\n        // to the outer body.\n        bodyBlockPath.node.body = innerBody;\n      }\n\n      const outerFnExpr = getOuterFnExpr(\n        this,\n        path as NodePath<\n          Exclude<t.Function, t.ArrowFunctionExpression | t.Method>\n        >,\n      );\n      // Note that getOuterFnExpr has the side-effect of ensuring that the\n      // function has a name (so node.id will always be an Identifier), even\n      // if a temporary name has to be synthesized.\n      t.assertIdentifier(\n        (node as t.FunctionDeclaration | t.FunctionExpression).id,\n      );\n\n      // Turn all declarations into vars, and replace the original\n      // declarations with equivalent assignment expressions.\n      const vars = hoist(\n        path as NodePath<t.FunctionDeclaration | t.FunctionExpression>,\n      );\n\n      const context = {\n        usesThis: false,\n        usesArguments: false,\n        getArgsId: () => t.cloneNode(argsId),\n      };\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      path.traverse(argumentsThisVisitor, context);\n\n      if (context.usesArguments) {\n        vars.push(\n          t.variableDeclarator(t.cloneNode(argsId), t.identifier(\"arguments\")),\n        );\n      }\n\n      const emitter = new Emitter(contextId, path.scope, vars, this);\n      emitter.explode(path.get(\"body\"));\n\n      if (vars.length > 0) {\n        outerBody.push(t.variableDeclaration(\"var\", vars));\n      }\n\n      const wrapArgs: any[] = [emitter.getContextFunction()];\n      const tryLocsList = emitter.getTryLocsList();\n\n      if (node.generator) {\n        wrapArgs.push(outerFnExpr);\n      } else if (context.usesThis || tryLocsList || node.async) {\n        // Async functions that are not generators don't care about the\n        // outer function because they don't need it to be marked and don't\n        // inherit from its .prototype.\n        wrapArgs.push(t.nullLiteral());\n      }\n      if (context.usesThis) {\n        wrapArgs.push(t.thisExpression());\n      } else if (tryLocsList || node.async) {\n        wrapArgs.push(t.nullLiteral());\n      }\n      if (tryLocsList) {\n        wrapArgs.push(tryLocsList);\n      } else if (node.async) {\n        wrapArgs.push(t.nullLiteral());\n      }\n\n      if (node.async) {\n        // Rename any locally declared \"Promise\" variable,\n        // to use the global one.\n        let currentScope = path.scope;\n        do {\n          if (currentScope.hasOwnBinding(\"Promise\"))\n            currentScope.rename(\"Promise\");\n        } while ((currentScope = currentScope.parent));\n\n        wrapArgs.push(t.identifier(\"Promise\"));\n      }\n\n      const wrapCall = t.callExpression(\n        process.env.BABEL_8_BREAKING || util.newHelpersAvailable(this)\n          ? !node.async\n            ? t.memberExpression(\n                t.callExpression(this.addHelper(\"regenerator\"), []),\n                t.identifier(\"w\"),\n              )\n            : node.generator\n              ? this.addHelper(\"regeneratorAsyncGen\")\n              : this.addHelper(\"regeneratorAsync\")\n          : util.runtimeProperty(this, node.async ? \"async\" : \"wrap\"),\n        wrapArgs,\n      );\n\n      outerBody.push(t.returnStatement(wrapCall));\n      node.body = t.blockStatement(outerBody);\n      // We injected a few new variable declarations (for every hoisted var),\n      // so we need to add them to the scope.\n      path.get(\"body.body\").forEach((p: any) => p.scope.registerDeclaration(p));\n\n      const oldDirectives = bodyBlockPath.node.directives;\n      if (oldDirectives) {\n        // Babylon represents directives like \"use strict\" as elements of\n        // a bodyBlockPath.node.directives array. (#248)\n        node.body.directives = oldDirectives;\n      }\n\n      const wasGeneratorFunction = node.generator;\n      if (wasGeneratorFunction) {\n        node.generator = false;\n      }\n\n      if (node.async) {\n        node.async = false;\n      }\n\n      if (wasGeneratorFunction && t.isExpression(node)) {\n        path.replaceWith(\n          t.callExpression(\n            process.env.BABEL_8_BREAKING || util.newHelpersAvailable(this)\n              ? t.memberExpression(\n                  t.callExpression(this.addHelper(\"regenerator\"), []),\n                  t.identifier(\"m\"),\n                )\n              : util.runtimeProperty(this, \"mark\"),\n            [node],\n          ),\n        );\n        path.addComment(\"leading\", \"#__PURE__\");\n      }\n\n      const insertedLocs = emitter.getInsertedLocs();\n\n      path.traverse({\n        NumericLiteral(path: any) {\n          if (!insertedLocs.has(path.node)) {\n            return;\n          }\n\n          path.replaceWith(t.numericLiteral(path.node.value));\n        },\n      });\n\n      // Generators are processed in 'exit' handlers so that regenerator only has to run on\n      // an ES5 AST, but that means traversal will not pick up newly inserted references\n      // to things like 'regeneratorRuntime'. To avoid this, we explicitly requeue.\n      path.requeue();\n    },\n  },\n});\n\n// Check if a node should be transformed by regenerator\nfunction shouldRegenerate(node: t.Function, state: any) {\n  if (node.generator) {\n    if (node.async) {\n      // Async generator\n      return state.opts.asyncGenerators !== false;\n    } else {\n      // Plain generator\n      return state.opts.generators !== false;\n    }\n  } else if (node.async) {\n    // Async function\n    return state.opts.async !== false;\n  } else {\n    // Not a generator or async function.\n    return false;\n  }\n}\n\n// Given a NodePath for a Function, return an Expression node that can be\n// used to refer reliably to the function object from inside the function.\n// This expression is essentially a replacement for arguments.callee, with\n// the key advantage that it works in strict mode.\nfunction getOuterFnExpr(\n  state: PluginPass,\n  funPath: NodePath<Exclude<t.Function, t.ArrowFunctionExpression | t.Method>>,\n) {\n  const node = funPath.node;\n  t.assertFunction(node);\n\n  if (!node.id) {\n    // Default-exported function declarations, and function expressions may not\n    // have a name to reference, so we explicitly add one.\n    node.id = funPath.scope.parent.generateUidIdentifier(\"callee\");\n  }\n\n  if (\n    node.generator && // Non-generator functions don't need to be marked.\n    t.isFunctionDeclaration(node)\n  ) {\n    // Return the identifier returned by runtime.mark(<node.id>).\n    return getMarkedFunctionId(state, funPath);\n  }\n\n  return t.cloneNode(node.id);\n}\n\nconst markInfo = new WeakMap();\n\nfunction getMarkInfo(node: any) {\n  if (!markInfo.has(node)) {\n    markInfo.set(node, {});\n  }\n  return markInfo.get(node);\n}\n\nfunction getMarkedFunctionId(\n  state: PluginPass,\n  funPath: NodePath<Exclude<t.Function, t.ArrowFunctionExpression | t.Method>>,\n) {\n  const node = funPath.node;\n  t.assertIdentifier(node.id);\n\n  const blockPath = funPath.findParent(function (path) {\n    return path.isProgram() || path.isBlockStatement();\n  }) as NodePath<t.Program | t.BlockStatement>;\n\n  if (!blockPath) {\n    return node.id;\n  }\n\n  const block = blockPath.node;\n  assert.ok(Array.isArray(block.body));\n\n  const info = getMarkInfo(block);\n  if (!info.decl) {\n    info.decl = t.variableDeclaration(\"var\", []);\n    blockPath.unshiftContainer(\"body\", info.decl);\n    info.declPath = blockPath.get(\"body.0\");\n  }\n\n  assert.strictEqual(info.declPath.node, info.decl);\n\n  // Get a new unique identifier for our marked variable.\n  const markedId = blockPath.scope.generateUidIdentifier(\"marked\");\n  const markCallExp = t.callExpression(\n    process.env.BABEL_8_BREAKING || util.newHelpersAvailable(state)\n      ? t.memberExpression(\n          t.callExpression(state.addHelper(\"regenerator\"), []),\n          t.identifier(\"m\"),\n        )\n      : util.runtimeProperty(state, \"mark\"),\n    [t.cloneNode(node.id)],\n  );\n\n  const index =\n    info.decl.declarations.push(t.variableDeclarator(markedId, markCallExp)) -\n    1;\n\n  const markCallExpPath = info.declPath.get(\"declarations.\" + index + \".init\");\n\n  assert.strictEqual(markCallExpPath.node, markCallExp);\n\n  markCallExpPath.addComment(\"leading\", \"#__PURE__\");\n\n  return t.cloneNode(markedId);\n}\n\nconst argumentsThisVisitor: Visitor<{\n  usesThis: boolean;\n  usesArguments: boolean;\n  getArgsId: () => t.Identifier;\n}> = {\n  \"FunctionExpression|FunctionDeclaration|Method\": function (path) {\n    path.skip();\n  },\n\n  Identifier: function (path, state) {\n    if (path.node.name === \"arguments\" && util.isReference(path)) {\n      path.replaceWith(state.getArgsId());\n      state.usesArguments = true;\n    }\n  },\n\n  ThisExpression: function (path, state) {\n    state.usesThis = true;\n  },\n};\n\nconst functionSentVisitor: Visitor<{\n  context: t.Identifier;\n  pluginPass: PluginPass;\n}> = {\n  MetaProperty(path, state) {\n    const { node } = path;\n\n    if (node.meta.name === \"function\" && node.property.name === \"sent\") {\n      path.replaceWith(\n        t.memberExpression(\n          t.cloneNode(state.context),\n          t.identifier(\n            process.env.BABEL_8_BREAKING ||\n              util.newHelpersAvailable(state.pluginPass)\n              ? \"v\"\n              : \"_sent\",\n          ),\n        ),\n      );\n    }\n  },\n};\n\nconst awaitVisitor: Visitor<PluginPass> = {\n  Function: function (path) {\n    path.skip(); // Don't descend into nested function scopes.\n  },\n\n  AwaitExpression: function (path) {\n    // Convert await expressions to yield expressions.\n    const argument = path.node.argument;\n\n    const helper =\n      // This is slightly tricky: newer versions of the `regeneratorRuntime`\n      // helper support using `awaitAsyncGenerator` as an alternative to\n      // `regeneratorRuntime().awrap`. There is no direct way to test if we\n      // have that part of the helper available, but we know that it has been\n      // introduced in the same version as `regeneratorKeys`.\n      process.env.BABEL_8_BREAKING || util.newHelpersAvailable(this)\n        ? this.addHelper(\"awaitAsyncGenerator\")\n        : util.runtimeProperty(this, \"awrap\");\n\n    // Transforming `await x` to `yield regeneratorRuntime.awrap(x)`\n    // causes the argument to be wrapped in such a way that the runtime\n    // can distinguish between awaited and merely yielded values.\n    path.replaceWith(\n      t.yieldExpression(t.callExpression(helper, [argument]), false),\n    );\n  },\n};\n"], "mappings": "AAAA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,UAAA;AAEb,IAAAC,OAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,6BAAA,GAAAH,OAAA;AACA,IAAAI,IAAA,GAAAJ,OAAA;AAEA,IAAAK,KAAA,GAAAL,OAAA;AAEO,MAAMF,UAAU,GAAGA,CAAA,MAA4B;EACpDQ,MAAMA,CAACC,IAAI,EAAEC,KAAK,EAAE;IAClB,MAAMC,IAAI,GAAGF,IAAI,CAACE,IAAI;IAEtB,IAAI,CAACC,gBAAgB,CAACD,IAAI,EAAED,KAAK,CAAC,EAAE;IAEpC,MAAMG,SAAS,GAAGC,WAAC,CAACC,kBAAkB,CACpC,IAAI,EACJ,EAAE,EACFD,WAAC,CAACE,SAAS,CAACL,IAAI,CAACM,IAAI,EAAE,KAAK,CAAC,EAC7BN,IAAI,CAACO,SAAS,EACdP,IAAI,CAACQ,KACP,CAAC;IAEDV,IAAI,CACDW,GAAG,CAAC,MAAM,CAAC,CACXC,GAAG,CAAC,MAAM,EAAE,CAACP,WAAC,CAACQ,eAAe,CAACR,WAAC,CAACS,cAAc,CAACV,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IAIpEF,IAAI,CAACQ,KAAK,GAAG,KAAK;IAClBR,IAAI,CAACO,SAAS,GAAG,KAAK;IAIpBT,IAAI,CAACW,GAAG,CAAC,6BAA6B,CAAC,CACvCI,yBAAyB,CAAC,CAAC;EAC/B,CAAC;EACDC,QAAQ,EAAE;IACRC,IAAIA,CACFjB,IAA8D,EAC9DC,KAAK,EACL;MACA,IAAIC,IAAI,GAAGF,IAAI,CAACE,IAAI;MAEpB,IAAI,CAACC,gBAAgB,CAACD,IAAI,EAAED,KAAK,CAAC,EAAE;MAGpCD,IAAI,GAAG,IAAAkB,qCAA4B,EAAClB,IAAI,CAAQ;MAChDE,IAAI,GAAGF,IAAI,CAACE,IAAI;MAEhB,MAAMiB,SAAS,GAAGnB,IAAI,CAACoB,KAAK,CAACC,qBAAqB,CAAC,SAAS,CAAC;MAC7D,MAAMC,MAAM,GAAGtB,IAAI,CAACoB,KAAK,CAACC,qBAAqB,CAAC,MAAM,CAAC;MAEvDrB,IAAI,CAACuB,WAAW,CAAC,CAAC;MAClB,MAAMC,aAAa,GAAGxB,IAAI,CAACW,GAAG,CAAC,MAAM,CAAC;MAEtC,IAAIT,IAAI,CAACQ,KAAK,EAAE;QAEdc,aAAa,CAACC,QAAQ,CAACC,YAAY,EAAE,IAAI,CAAC;MAC5C;MAGAF,aAAa,CAACC,QAAQ,CAACE,mBAAmB,EAAE;QAC1CC,OAAO,EAAET,SAAS;QAClBU,UAAU,EAAE;MACd,CAAC,CAAC;MAEF,MAAMC,SAAgB,GAAG,EAAE;MAC3B,MAAMC,SAAgB,GAAG,EAAE;MAE3BP,aAAa,CAACb,GAAG,CAAC,MAAM,CAAC,CAACqB,OAAO,CAAC,UAAUC,SAAc,EAAE;QAC1D,MAAM/B,IAAI,GAAG+B,SAAS,CAAC/B,IAAI;QAC3B,IACEG,WAAC,CAAC6B,qBAAqB,CAAChC,IAAI,CAAC,IAC7BG,WAAC,CAAC8B,eAAe,CAACjC,IAAI,CAACkC,UAAU,CAAC,EAClC;UAMAN,SAAS,CAACO,IAAI,CAACnC,IAAI,CAAC;QACtB,CAAC,MAAM,IAAI,CAAAA,IAAI,oBAAJA,IAAI,CAAEoC,WAAW,KAAI,IAAI,EAAE;UACpCR,SAAS,CAACO,IAAI,CAACnC,IAAI,CAAC;QACtB,CAAC,MAAM;UACL6B,SAAS,CAACM,IAAI,CAACnC,IAAI,CAAC;QACtB;MACF,CAAC,CAAC;MAEF,IAAI4B,SAAS,CAACS,MAAM,GAAG,CAAC,EAAE;QAGxBf,aAAa,CAACtB,IAAI,CAACM,IAAI,GAAGuB,SAAS;MACrC;MAEA,MAAMS,WAAW,GAAGC,cAAc,CAChC,IAAI,EACJzC,IAGF,CAAC;MAIDK,WAAC,CAACqC,gBAAgB,CACfxC,IAAI,CAAkDyC,EACzD,CAAC;MAID,MAAMC,IAAI,GAAG,IAAAC,YAAK,EAChB7C,IACF,CAAC;MAED,MAAM4B,OAAO,GAAG;QACdkB,QAAQ,EAAE,KAAK;QACfC,aAAa,EAAE,KAAK;QACpBC,SAAS,EAAEA,CAAA,KAAM3C,WAAC,CAACE,SAAS,CAACe,MAAM;MACrC,CAAC;MAEDtB,IAAI,CAACyB,QAAQ,CAACwB,oBAAoB,EAAErB,OAAO,CAAC;MAE5C,IAAIA,OAAO,CAACmB,aAAa,EAAE;QACzBH,IAAI,CAACP,IAAI,CACPhC,WAAC,CAAC6C,kBAAkB,CAAC7C,WAAC,CAACE,SAAS,CAACe,MAAM,CAAC,EAAEjB,WAAC,CAAC8C,UAAU,CAAC,WAAW,CAAC,CACrE,CAAC;MACH;MAEA,MAAMC,OAAO,GAAG,IAAIC,aAAO,CAAClC,SAAS,EAAEnB,IAAI,CAACoB,KAAK,EAAEwB,IAAI,EAAE,IAAI,CAAC;MAC9DQ,OAAO,CAACE,OAAO,CAACtD,IAAI,CAACW,GAAG,CAAC,MAAM,CAAC,CAAC;MAEjC,IAAIiC,IAAI,CAACL,MAAM,GAAG,CAAC,EAAE;QACnBT,SAAS,CAACO,IAAI,CAAChC,WAAC,CAACkD,mBAAmB,CAAC,KAAK,EAAEX,IAAI,CAAC,CAAC;MACpD;MAEA,MAAMY,QAAe,GAAG,CAACJ,OAAO,CAACK,kBAAkB,CAAC,CAAC,CAAC;MACtD,MAAMC,WAAW,GAAGN,OAAO,CAACO,cAAc,CAAC,CAAC;MAE5C,IAAIzD,IAAI,CAACO,SAAS,EAAE;QAClB+C,QAAQ,CAACnB,IAAI,CAACG,WAAW,CAAC;MAC5B,CAAC,MAAM,IAAIZ,OAAO,CAACkB,QAAQ,IAAIY,WAAW,IAAIxD,IAAI,CAACQ,KAAK,EAAE;QAIxD8C,QAAQ,CAACnB,IAAI,CAAChC,WAAC,CAACuD,WAAW,CAAC,CAAC,CAAC;MAChC;MACA,IAAIhC,OAAO,CAACkB,QAAQ,EAAE;QACpBU,QAAQ,CAACnB,IAAI,CAAChC,WAAC,CAACwD,cAAc,CAAC,CAAC,CAAC;MACnC,CAAC,MAAM,IAAIH,WAAW,IAAIxD,IAAI,CAACQ,KAAK,EAAE;QACpC8C,QAAQ,CAACnB,IAAI,CAAChC,WAAC,CAACuD,WAAW,CAAC,CAAC,CAAC;MAChC;MACA,IAAIF,WAAW,EAAE;QACfF,QAAQ,CAACnB,IAAI,CAACqB,WAAW,CAAC;MAC5B,CAAC,MAAM,IAAIxD,IAAI,CAACQ,KAAK,EAAE;QACrB8C,QAAQ,CAACnB,IAAI,CAAChC,WAAC,CAACuD,WAAW,CAAC,CAAC,CAAC;MAChC;MAEA,IAAI1D,IAAI,CAACQ,KAAK,EAAE;QAGd,IAAIoD,YAAY,GAAG9D,IAAI,CAACoB,KAAK;QAC7B,GAAG;UACD,IAAI0C,YAAY,CAACC,aAAa,CAAC,SAAS,CAAC,EACvCD,YAAY,CAACE,MAAM,CAAC,SAAS,CAAC;QAClC,CAAC,QAASF,YAAY,GAAGA,YAAY,CAACG,MAAM;QAE5CT,QAAQ,CAACnB,IAAI,CAAChC,WAAC,CAAC8C,UAAU,CAAC,SAAS,CAAC,CAAC;MACxC;MAEA,MAAMe,QAAQ,GAAG7D,WAAC,CAACS,cAAc,CACCjB,IAAI,CAACsE,mBAAmB,CAAC,IAAI,CAAC,GAC1D,CAACjE,IAAI,CAACQ,KAAK,GACTL,WAAC,CAAC+D,gBAAgB,CAChB/D,WAAC,CAACS,cAAc,CAAC,IAAI,CAACuD,SAAS,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC,EACnDhE,WAAC,CAAC8C,UAAU,CAAC,GAAG,CAClB,CAAC,GACDjD,IAAI,CAACO,SAAS,GACZ,IAAI,CAAC4D,SAAS,CAAC,qBAAqB,CAAC,GACrC,IAAI,CAACA,SAAS,CAAC,kBAAkB,CAAC,GACtCxE,IAAI,CAACyE,eAAe,CAAC,IAAI,EAAEpE,IAAI,CAACQ,KAAK,GAAG,OAAO,GAAG,MAAM,CAAC,EAC7D8C,QACF,CAAC;MAED1B,SAAS,CAACO,IAAI,CAAChC,WAAC,CAACQ,eAAe,CAACqD,QAAQ,CAAC,CAAC;MAC3ChE,IAAI,CAACM,IAAI,GAAGH,WAAC,CAACkE,cAAc,CAACzC,SAAS,CAAC;MAGvC9B,IAAI,CAACW,GAAG,CAAC,WAAW,CAAC,CAACqB,OAAO,CAAEwC,CAAM,IAAKA,CAAC,CAACpD,KAAK,CAACqD,mBAAmB,CAACD,CAAC,CAAC,CAAC;MAEzE,MAAME,aAAa,GAAGlD,aAAa,CAACtB,IAAI,CAACyE,UAAU;MACnD,IAAID,aAAa,EAAE;QAGjBxE,IAAI,CAACM,IAAI,CAACmE,UAAU,GAAGD,aAAa;MACtC;MAEA,MAAME,oBAAoB,GAAG1E,IAAI,CAACO,SAAS;MAC3C,IAAImE,oBAAoB,EAAE;QACxB1E,IAAI,CAACO,SAAS,GAAG,KAAK;MACxB;MAEA,IAAIP,IAAI,CAACQ,KAAK,EAAE;QACdR,IAAI,CAACQ,KAAK,GAAG,KAAK;MACpB;MAEA,IAAIkE,oBAAoB,IAAIvE,WAAC,CAACwE,YAAY,CAAC3E,IAAI,CAAC,EAAE;QAChDF,IAAI,CAAC8E,WAAW,CACdzE,WAAC,CAACS,cAAc,CACkBjB,IAAI,CAACsE,mBAAmB,CAAC,IAAI,CAAC,GAC1D9D,WAAC,CAAC+D,gBAAgB,CAChB/D,WAAC,CAACS,cAAc,CAAC,IAAI,CAACuD,SAAS,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC,EACnDhE,WAAC,CAAC8C,UAAU,CAAC,GAAG,CAClB,CAAC,GACDtD,IAAI,CAACyE,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,EACtC,CAACpE,IAAI,CACP,CACF,CAAC;QACDF,IAAI,CAAC+E,UAAU,CAAC,SAAS,EAAE,WAAW,CAAC;MACzC;MAEA,MAAMC,YAAY,GAAG5B,OAAO,CAAC6B,eAAe,CAAC,CAAC;MAE9CjF,IAAI,CAACyB,QAAQ,CAAC;QACZyD,cAAcA,CAAClF,IAAS,EAAE;UACxB,IAAI,CAACgF,YAAY,CAACG,GAAG,CAACnF,IAAI,CAACE,IAAI,CAAC,EAAE;YAChC;UACF;UAEAF,IAAI,CAAC8E,WAAW,CAACzE,WAAC,CAAC+E,cAAc,CAACpF,IAAI,CAACE,IAAI,CAACZ,KAAK,CAAC,CAAC;QACrD;MACF,CAAC,CAAC;MAKFU,IAAI,CAACqF,OAAO,CAAC,CAAC;IAChB;EACF;AACF,CAAC,CAAC;AAAChG,OAAA,CAAAE,UAAA,GAAAA,UAAA;AAGH,SAASY,gBAAgBA,CAACD,IAAgB,EAAED,KAAU,EAAE;EACtD,IAAIC,IAAI,CAACO,SAAS,EAAE;IAClB,IAAIP,IAAI,CAACQ,KAAK,EAAE;MAEd,OAAOT,KAAK,CAACqF,IAAI,CAACC,eAAe,KAAK,KAAK;IAC7C,CAAC,MAAM;MAEL,OAAOtF,KAAK,CAACqF,IAAI,CAACE,UAAU,KAAK,KAAK;IACxC;EACF,CAAC,MAAM,IAAItF,IAAI,CAACQ,KAAK,EAAE;IAErB,OAAOT,KAAK,CAACqF,IAAI,CAAC5E,KAAK,KAAK,KAAK;EACnC,CAAC,MAAM;IAEL,OAAO,KAAK;EACd;AACF;AAMA,SAAS+B,cAAcA,CACrBxC,KAAiB,EACjBwF,OAA4E,EAC5E;EACA,MAAMvF,IAAI,GAAGuF,OAAO,CAACvF,IAAI;EACzBG,WAAC,CAACqF,cAAc,CAACxF,IAAI,CAAC;EAEtB,IAAI,CAACA,IAAI,CAACyC,EAAE,EAAE;IAGZzC,IAAI,CAACyC,EAAE,GAAG8C,OAAO,CAACrE,KAAK,CAAC6C,MAAM,CAAC5C,qBAAqB,CAAC,QAAQ,CAAC;EAChE;EAEA,IACEnB,IAAI,CAACO,SAAS,IACdJ,WAAC,CAACsF,qBAAqB,CAACzF,IAAI,CAAC,EAC7B;IAEA,OAAO0F,mBAAmB,CAAC3F,KAAK,EAAEwF,OAAO,CAAC;EAC5C;EAEA,OAAOpF,WAAC,CAACE,SAAS,CAACL,IAAI,CAACyC,EAAE,CAAC;AAC7B;AAEA,MAAMkD,QAAQ,GAAG,IAAIC,OAAO,CAAC,CAAC;AAE9B,SAASC,WAAWA,CAAC7F,IAAS,EAAE;EAC9B,IAAI,CAAC2F,QAAQ,CAACV,GAAG,CAACjF,IAAI,CAAC,EAAE;IACvB2F,QAAQ,CAACjF,GAAG,CAACV,IAAI,EAAE,CAAC,CAAC,CAAC;EACxB;EACA,OAAO2F,QAAQ,CAAClF,GAAG,CAACT,IAAI,CAAC;AAC3B;AAEA,SAAS0F,mBAAmBA,CAC1B3F,KAAiB,EACjBwF,OAA4E,EAC5E;EACA,MAAMvF,IAAI,GAAGuF,OAAO,CAACvF,IAAI;EACzBG,WAAC,CAACqC,gBAAgB,CAACxC,IAAI,CAACyC,EAAE,CAAC;EAE3B,MAAMqD,SAAS,GAAGP,OAAO,CAACQ,UAAU,CAAC,UAAUjG,IAAI,EAAE;IACnD,OAAOA,IAAI,CAACkG,SAAS,CAAC,CAAC,IAAIlG,IAAI,CAACmG,gBAAgB,CAAC,CAAC;EACpD,CAAC,CAA2C;EAE5C,IAAI,CAACH,SAAS,EAAE;IACd,OAAO9F,IAAI,CAACyC,EAAE;EAChB;EAEA,MAAMyD,KAAK,GAAGJ,SAAS,CAAC9F,IAAI;EAC5BmG,OAAM,CAACC,EAAE,CAACC,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC5F,IAAI,CAAC,CAAC;EAEpC,MAAMiG,IAAI,GAAGV,WAAW,CAACK,KAAK,CAAC;EAC/B,IAAI,CAACK,IAAI,CAACC,IAAI,EAAE;IACdD,IAAI,CAACC,IAAI,GAAGrG,WAAC,CAACkD,mBAAmB,CAAC,KAAK,EAAE,EAAE,CAAC;IAC5CyC,SAAS,CAACW,gBAAgB,CAAC,MAAM,EAAEF,IAAI,CAACC,IAAI,CAAC;IAC7CD,IAAI,CAACG,QAAQ,GAAGZ,SAAS,CAACrF,GAAG,CAAC,QAAQ,CAAC;EACzC;EAEA0F,OAAM,CAACQ,WAAW,CAACJ,IAAI,CAACG,QAAQ,CAAC1G,IAAI,EAAEuG,IAAI,CAACC,IAAI,CAAC;EAGjD,MAAMI,QAAQ,GAAGd,SAAS,CAAC5E,KAAK,CAACC,qBAAqB,CAAC,QAAQ,CAAC;EAChE,MAAM0F,WAAW,GAAG1G,WAAC,CAACS,cAAc,CACFjB,IAAI,CAACsE,mBAAmB,CAAClE,KAAK,CAAC,GAC3DI,WAAC,CAAC+D,gBAAgB,CAChB/D,WAAC,CAACS,cAAc,CAACb,KAAK,CAACoE,SAAS,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC,EACpDhE,WAAC,CAAC8C,UAAU,CAAC,GAAG,CAClB,CAAC,GACDtD,IAAI,CAACyE,eAAe,CAACrE,KAAK,EAAE,MAAM,CAAC,EACvC,CAACI,WAAC,CAACE,SAAS,CAACL,IAAI,CAACyC,EAAE,CAAC,CACvB,CAAC;EAED,MAAMqE,KAAK,GACTP,IAAI,CAACC,IAAI,CAACO,YAAY,CAAC5E,IAAI,CAAChC,WAAC,CAAC6C,kBAAkB,CAAC4D,QAAQ,EAAEC,WAAW,CAAC,CAAC,GACxE,CAAC;EAEH,MAAMG,eAAe,GAAGT,IAAI,CAACG,QAAQ,CAACjG,GAAG,CAAC,eAAe,GAAGqG,KAAK,GAAG,OAAO,CAAC;EAE5EX,OAAM,CAACQ,WAAW,CAACK,eAAe,CAAChH,IAAI,EAAE6G,WAAW,CAAC;EAErDG,eAAe,CAACnC,UAAU,CAAC,SAAS,EAAE,WAAW,CAAC;EAElD,OAAO1E,WAAC,CAACE,SAAS,CAACuG,QAAQ,CAAC;AAC9B;AAEA,MAAM7D,oBAIJ,GAAG;EACH,+CAA+C,EAAE,SAAAkE,CAAUnH,IAAI,EAAE;IAC/DA,IAAI,CAACoH,IAAI,CAAC,CAAC;EACb,CAAC;EAEDC,UAAU,EAAE,SAAAA,CAAUrH,IAAI,EAAEC,KAAK,EAAE;IACjC,IAAID,IAAI,CAACE,IAAI,CAACoH,IAAI,KAAK,WAAW,IAAIzH,IAAI,CAAC0H,WAAW,CAACvH,IAAI,CAAC,EAAE;MAC5DA,IAAI,CAAC8E,WAAW,CAAC7E,KAAK,CAAC+C,SAAS,CAAC,CAAC,CAAC;MACnC/C,KAAK,CAAC8C,aAAa,GAAG,IAAI;IAC5B;EACF,CAAC;EAEDyE,cAAc,EAAE,SAAAA,CAAUxH,IAAI,EAAEC,KAAK,EAAE;IACrCA,KAAK,CAAC6C,QAAQ,GAAG,IAAI;EACvB;AACF,CAAC;AAED,MAAMnB,mBAGJ,GAAG;EACH8F,YAAYA,CAACzH,IAAI,EAAEC,KAAK,EAAE;IACxB,MAAM;MAAEC;IAAK,CAAC,GAAGF,IAAI;IAErB,IAAIE,IAAI,CAACwH,IAAI,CAACJ,IAAI,KAAK,UAAU,IAAIpH,IAAI,CAACyH,QAAQ,CAACL,IAAI,KAAK,MAAM,EAAE;MAClEtH,IAAI,CAAC8E,WAAW,CACdzE,WAAC,CAAC+D,gBAAgB,CAChB/D,WAAC,CAACE,SAAS,CAACN,KAAK,CAAC2B,OAAO,CAAC,EAC1BvB,WAAC,CAAC8C,UAAU,CAERtD,IAAI,CAACsE,mBAAmB,CAAClE,KAAK,CAAC4B,UAAU,CAAC,GACxC,GAAG,GACH,OACN,CACF,CACF,CAAC;IACH;EACF;AACF,CAAC;AAED,MAAMH,YAAiC,GAAG;EACxCV,QAAQ,EAAE,SAAAA,CAAUhB,IAAI,EAAE;IACxBA,IAAI,CAACoH,IAAI,CAAC,CAAC;EACb,CAAC;EAEDQ,eAAe,EAAE,SAAAA,CAAU5H,IAAI,EAAE;IAE/B,MAAM6H,QAAQ,GAAG7H,IAAI,CAACE,IAAI,CAAC2H,QAAQ;IAEnC,MAAMC,MAAM,GAMsBjI,IAAI,CAACsE,mBAAmB,CAAC,IAAI,CAAC,GAC1D,IAAI,CAACE,SAAS,CAAC,qBAAqB,CAAC,GACrCxE,IAAI,CAACyE,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC;IAKzCtE,IAAI,CAAC8E,WAAW,CACdzE,WAAC,CAAC0H,eAAe,CAAC1H,WAAC,CAACS,cAAc,CAACgH,MAAM,EAAE,CAACD,QAAQ,CAAC,CAAC,EAAE,KAAK,CAC/D,CAAC;EACH;AACF,CAAC", "ignoreList": []}