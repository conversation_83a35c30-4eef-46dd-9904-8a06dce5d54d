{"name": "@expo/osascript", "version": "2.3.7", "description": "Tools for running an osascripts in Node", "license": "MIT", "keywords": ["osascript", "mac", "osx", "spawn", "exec"], "homepage": "https://github.com/expo/expo/tree/main/packages/@expo/osascript#readme", "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/@expo/osascript"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "main": "build/index.js", "files": ["build"], "scripts": {"build": "expo-module tsc", "prepare": "yarn run clean && yarn run build", "clean": "expo-module clean", "lint": "expo-module lint", "typecheck": "expo-module typecheck", "test": "expo-module test", "watch": "yarn run build --watch --preserveWatchOutput", "prepublishOnly": "expo-module prepublishOnly"}, "engines": {"node": ">=12"}, "dependencies": {"@expo/spawn-async": "^1.7.2", "exec-async": "^2.2.0"}, "devDependencies": {"expo-module-scripts": "^5.0.7"}, "publishConfig": {"access": "public"}, "gitHead": "088e79428be97cf3ee11fc93e0e5a1fc1c8bea1e"}