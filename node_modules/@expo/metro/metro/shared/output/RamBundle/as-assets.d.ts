/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 * @oncall react_native
 */

import type { RamBundleInfo } from "../../../DeltaBundler/Serializers/getRamBundleInfo";
import type { OutputOptions } from "../../types.flow";
/**
 * Saves all JS modules of an app as single files
 * The startup code (prelude, polyfills etc.) are written to the file
 * designated by the `bundleOuput` option.
 * All other modules go into a 'js-modules' folder that in the same parent
 * directory as the startup file.
 */
declare function saveAsAssets(bundle: RamBundleInfo, options: OutputOptions, log: (...args: Array<string>) => void): Promise<any>;
export default saveAsAssets;