/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 * @oncall react_native
 */

import type { RevisionId } from "../IncrementalBundler";
declare class RevisionNotFoundError extends Error {
  revisionId: RevisionId;
  constructor(revisionId: RevisionId);
}
export default RevisionNotFoundError;