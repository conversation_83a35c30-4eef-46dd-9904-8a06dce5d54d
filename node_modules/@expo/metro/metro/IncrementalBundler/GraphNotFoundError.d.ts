/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 * @oncall react_native
 */

import type { GraphId } from "../lib/getGraphId";
declare class GraphNotFoundError extends Error {
  graphId: GraphId;
  constructor(graphId: GraphId);
}
export default GraphNotFoundError;