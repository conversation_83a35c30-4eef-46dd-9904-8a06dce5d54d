/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 * @oncall react_native
 */

import type { CustomTransformOptions } from "../../metro-transform-worker";
declare const $$EXPORT_DEFAULT_DECLARATION$$: (urlObj: {
  readonly query?: {
    [$$Key$$: string]: string;
  };
}) => CustomTransformOptions;
export default $$EXPORT_DEFAULT_DECLARATION$$;