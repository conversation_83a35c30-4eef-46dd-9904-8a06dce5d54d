/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 * @oncall react_native
 */

import type { BundleOptions, SplitBundleOptions } from "../shared/types.flow";
/**
 * Splits a BundleOptions object into smaller, more manageable parts.
 */
declare function splitBundleOptions(options: BundleOptions): SplitBundleOptions;
export default splitBundleOptions;