/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 * @oncall react_native
 */

import type { Module } from "../types.flow";
import type { SourceMapGeneratorOptions } from "./sourceMapGenerator";
export declare function sourceMapString(modules: ReadonlyArray<Module>, options: SourceMapGeneratorOptions): string;
export declare function sourceMapStringNonBlocking(modules: ReadonlyArray<Module>, options: SourceMapGeneratorOptions): Promise<string>;