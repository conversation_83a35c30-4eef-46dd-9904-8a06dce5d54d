/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 * @oncall react_native
 */

import type { Module, ReadOnlyGraph } from "../types.flow";
export interface Options {
  platform?: null | string;
  readonly processModuleFilter: (module: Module) => boolean;
}
declare function getAllFiles(pre: ReadonlyArray<Module>, graph: ReadOnlyGraph, options: Options): Promise<ReadonlyArray<string>>;
export default getAllFiles;