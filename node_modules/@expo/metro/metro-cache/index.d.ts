// See: https://github.com/facebook/metro/blob/v0.82.0/packages/metro-cache/src/index.js

import Cache from './Cache';
import stableHash from './stableHash';
import AutoCleanFileStore from './stores/AutoCleanFileStore';
import FileStore from './stores/FileStore';
import HttpGetStore from './stores/HttpGetStore';
import HttpStore from './stores/HttpStore';

export type { Options as FileOptions } from './stores/FileStore';
export type { Options as HttpOptions } from './stores/HttpStore';
export type { CacheStore } from './types.flow';

// NOTE(cedric): this is for original typescript compatibility
export interface MetroCache {
  AutoCleanFileStore: typeof AutoCleanFileStore;
  Cache: typeof Cache;
  FileStore: typeof FileStore;
  HttpGetStore: typeof HttpGetStore;
  HttpStore: typeof HttpStore;
  stableHash: typeof stableHash;
}

export {
  AutoCleanFileStore,
  Cache,
  FileStore,
  HttpGetStore,
  HttpStore,
  stableHash,
};
