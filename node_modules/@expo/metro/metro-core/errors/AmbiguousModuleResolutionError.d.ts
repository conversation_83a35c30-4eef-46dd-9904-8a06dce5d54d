/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 * @oncall react_native
 */

import type { DuplicateHasteCandidatesError } from "../../metro-file-map";
declare class AmbiguousModuleResolutionError extends Error {
  fromModulePath: string;
  hasteError: DuplicateHasteCandidatesError;
  constructor(fromModulePath: string, hasteError: DuplicateHasteCandidatesError);
}
export default AmbiguousModuleResolutionError;