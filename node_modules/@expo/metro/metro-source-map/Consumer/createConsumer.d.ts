/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 * @oncall react_native
 */

import type { MixedSourceMap } from "../source-map";
import type { IConsumer } from "./types.flow";
declare function createConsumer(sourceMap: MixedSourceMap): IConsumer;
export default createConsumer;