/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 * @oncall react_native
 */

import type { IterationOrder, LookupBias } from "./constants";
export type { IterationOrder, LookupBias };
export interface GeneratedOffset {
  readonly lines: number;
  readonly columns: number;
}
export interface SourcePosition {
  source?: null | string;
  line?: null | number;
  column?: null | number;
  name?: null | string;
}
export interface GeneratedPosition {
  readonly line: number;
  readonly column: number;
}
export interface GeneratedPositionLookup {
  readonly line?: null | number;
  readonly column?: null | number;
  readonly bias?: LookupBias;
}
export interface Mapping {
  readonly source?: null | string;
  readonly generatedLine: number;
  readonly generatedColumn: number;
  readonly originalLine?: null | number;
  readonly originalColumn?: null | number;
  readonly name?: null | string;
}
export interface IConsumer {
  originalPositionFor(generatedPosition: GeneratedPositionLookup): SourcePosition;
  generatedMappings(): Iterable<Mapping>;
  eachMapping(callback: (mapping: Mapping) => any, context?: any, order?: IterationOrder): void;
  get file(): null | undefined | string;
  sourceContentFor(source: string, nullOnMissing: true): null | undefined | string;
}