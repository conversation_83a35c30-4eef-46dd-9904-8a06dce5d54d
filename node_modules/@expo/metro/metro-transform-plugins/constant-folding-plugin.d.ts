/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 * @oncall react_native
 */

import type { PluginObj } from "@babel/core";
import type $$IMPORT_TYPEOF_1$$ from "@babel/traverse";
type Traverse = typeof $$IMPORT_TYPEOF_1$$;
import * as Types from "@babel/types";
export interface State {
  stripped: boolean;
}
declare function constantFoldingPlugin(context: {
  types: typeof Types;
  traverse: Traverse;
}): PluginObj<State>;
export default constantFoldingPlugin;