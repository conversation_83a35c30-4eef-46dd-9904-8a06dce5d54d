{"name": "@expo/config-types", "version": "54.0.8", "description": "Types for the Expo config object app.config.ts", "types": "build/ExpoConfig.d.ts", "main": "build/ExpoConfig.js", "scripts": {"build": "expo-module tsc", "clean": "expo-module clean", "generate": "node ./scripts/generate.js", "lint": "expo-module lint", "prepare": "expo-module clean && expo-module tsc", "prepublishOnly": "expo-module prepublishOnly", "test": "expo-module test", "typecheck": "expo-module typecheck", "watch": "expo-module tsc --watch --preserveWatchOutput"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/@expo/config-types"}, "keywords": ["json", "app.json", "app.config.js", "react-native", "expo", "react"], "license": "MIT", "bugs": {"url": "https://github.com/expo/expo/issues"}, "homepage": "https://github.com/expo/expo/tree/main/packages/@expo/config-types#readme", "files": ["build"], "devDependencies": {"expo-module-scripts": "^5.0.7", "json-schema-to-typescript": "^14.0.5"}, "publishConfig": {"access": "public"}, "gitHead": "088e79428be97cf3ee11fc93e0e5a1fc1c8bea1e"}