"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.WindowSoftInputMode = exports.Version = exports.Updates = exports.Styles = exports.Strings = exports.StatusBar = exports.Scheme = exports.Resources = exports.Properties = exports.PrimaryColor = exports.PredictiveBackGesture = exports.Permissions = exports.Paths = exports.Package = exports.Orientation = exports.Name = exports.Manifest = exports.Locales = exports.IntentFilters = exports.GoogleServices = exports.GoogleMapsApiKey = exports.EasBuild = exports.Colors = exports.CodeMod = exports.BuildProperties = exports.AllowBackup = void 0;
function AllowBackup() {
  const data = _interopRequireWildcard(require("./AllowBackup"));
  AllowBackup = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "AllowBackup", {
  enumerable: true,
  get: function () {
    return AllowBackup();
  }
});
function BuildProperties() {
  const data = _interopRequireWildcard(require("./BuildProperties"));
  BuildProperties = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "BuildProperties", {
  enumerable: true,
  get: function () {
    return BuildProperties();
  }
});
function Colors() {
  const data = _interopRequireWildcard(require("./Colors"));
  Colors = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Colors", {
  enumerable: true,
  get: function () {
    return Colors();
  }
});
function EasBuild() {
  const data = _interopRequireWildcard(require("./EasBuild"));
  EasBuild = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "EasBuild", {
  enumerable: true,
  get: function () {
    return EasBuild();
  }
});
function GoogleMapsApiKey() {
  const data = _interopRequireWildcard(require("./GoogleMapsApiKey"));
  GoogleMapsApiKey = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "GoogleMapsApiKey", {
  enumerable: true,
  get: function () {
    return GoogleMapsApiKey();
  }
});
function GoogleServices() {
  const data = _interopRequireWildcard(require("./GoogleServices"));
  GoogleServices = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "GoogleServices", {
  enumerable: true,
  get: function () {
    return GoogleServices();
  }
});
function IntentFilters() {
  const data = _interopRequireWildcard(require("./IntentFilters"));
  IntentFilters = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "IntentFilters", {
  enumerable: true,
  get: function () {
    return IntentFilters();
  }
});
function Locales() {
  const data = _interopRequireWildcard(require("./Locales"));
  Locales = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Locales", {
  enumerable: true,
  get: function () {
    return Locales();
  }
});
function Manifest() {
  const data = _interopRequireWildcard(require("./Manifest"));
  Manifest = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Manifest", {
  enumerable: true,
  get: function () {
    return Manifest();
  }
});
function Name() {
  const data = _interopRequireWildcard(require("./Name"));
  Name = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Name", {
  enumerable: true,
  get: function () {
    return Name();
  }
});
function Orientation() {
  const data = _interopRequireWildcard(require("./Orientation"));
  Orientation = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Orientation", {
  enumerable: true,
  get: function () {
    return Orientation();
  }
});
function Package() {
  const data = _interopRequireWildcard(require("./Package"));
  Package = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Package", {
  enumerable: true,
  get: function () {
    return Package();
  }
});
function Paths() {
  const data = _interopRequireWildcard(require("./Paths"));
  Paths = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Paths", {
  enumerable: true,
  get: function () {
    return Paths();
  }
});
function Permissions() {
  const data = _interopRequireWildcard(require("./Permissions"));
  Permissions = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Permissions", {
  enumerable: true,
  get: function () {
    return Permissions();
  }
});
function PredictiveBackGesture() {
  const data = _interopRequireWildcard(require("./PredictiveBackGesture"));
  PredictiveBackGesture = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "PredictiveBackGesture", {
  enumerable: true,
  get: function () {
    return PredictiveBackGesture();
  }
});
function PrimaryColor() {
  const data = _interopRequireWildcard(require("./PrimaryColor"));
  PrimaryColor = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "PrimaryColor", {
  enumerable: true,
  get: function () {
    return PrimaryColor();
  }
});
function Properties() {
  const data = _interopRequireWildcard(require("./Properties"));
  Properties = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Properties", {
  enumerable: true,
  get: function () {
    return Properties();
  }
});
function Resources() {
  const data = _interopRequireWildcard(require("./Resources"));
  Resources = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Resources", {
  enumerable: true,
  get: function () {
    return Resources();
  }
});
function Scheme() {
  const data = _interopRequireWildcard(require("./Scheme"));
  Scheme = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Scheme", {
  enumerable: true,
  get: function () {
    return Scheme();
  }
});
function StatusBar() {
  const data = _interopRequireWildcard(require("./StatusBar"));
  StatusBar = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "StatusBar", {
  enumerable: true,
  get: function () {
    return StatusBar();
  }
});
function Strings() {
  const data = _interopRequireWildcard(require("./Strings"));
  Strings = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Strings", {
  enumerable: true,
  get: function () {
    return Strings();
  }
});
function Styles() {
  const data = _interopRequireWildcard(require("./Styles"));
  Styles = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Styles", {
  enumerable: true,
  get: function () {
    return Styles();
  }
});
function Updates() {
  const data = _interopRequireWildcard(require("./Updates"));
  Updates = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Updates", {
  enumerable: true,
  get: function () {
    return Updates();
  }
});
function Version() {
  const data = _interopRequireWildcard(require("./Version"));
  Version = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "Version", {
  enumerable: true,
  get: function () {
    return Version();
  }
});
function WindowSoftInputMode() {
  const data = _interopRequireWildcard(require("./WindowSoftInputMode"));
  WindowSoftInputMode = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "WindowSoftInputMode", {
  enumerable: true,
  get: function () {
    return WindowSoftInputMode();
  }
});
function CodeMod() {
  const data = _interopRequireWildcard(require("./codeMod"));
  CodeMod = function () {
    return data;
  };
  return data;
}
Object.defineProperty(exports, "CodeMod", {
  enumerable: true,
  get: function () {
    return CodeMod();
  }
});
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
//# sourceMappingURL=index.js.map