{"version": 3, "file": "index.js", "names": ["AndroidConfig", "data", "_interopRequireWildcard", "require", "Object", "defineProperty", "exports", "enumerable", "get", "IOSConfig", "_createBaseMod", "_withAndroidBaseMods", "_withIosBaseMods", "XML", "CodeGenerator", "History", "WarningAggregator", "_Updates", "_Plugin", "keys", "for<PERSON>ach", "key", "prototype", "hasOwnProperty", "call", "_exportNames", "_withPlugins", "_withRunOnce", "_withDangerousMod", "_withFinalizedMod", "_withMod", "_iosPlugins", "_androidPlugins", "_withStaticPlugin", "_modCompiler", "_errors", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "n", "__proto__", "a", "getOwnPropertyDescriptor", "u", "i", "set", "BaseMods", "withGeneratedBaseMods", "provider", "withAndroidBaseMods", "getAndroidModFileProviders", "withIosBaseMods", "getIosModFileProviders"], "sources": ["../src/index.ts"], "sourcesContent": ["/**\n * For internal use in Expo CLI\n */\nimport * as AndroidConfig from './android';\nimport * as IOSConfig from './ios';\nimport { provider, withGeneratedBaseMods } from './plugins/createBaseMod';\nimport { getAndroidModFileProviders, withAndroidBaseMods } from './plugins/withAndroidBaseMods';\nimport { getIosModFileProviders, withIosBaseMods } from './plugins/withIosBaseMods';\nimport * as XML from './utils/XML';\nimport * as CodeGenerator from './utils/generateCode';\nimport * as History from './utils/history';\nimport * as WarningAggregator from './utils/warnings';\n\n// TODO: Remove\nexport * as Updates from './utils/Updates';\n\nexport { IOSConfig, AndroidConfig };\n\nexport { WarningAggregator, CodeGenerator, History, XML };\n\n/**\n * These are the \"config-plugins\"\n */\n\nexport * from './Plugin.types';\n\nexport { withPlugins } from './plugins/withPlugins';\n\nexport { withRunOnce, createRunOncePlugin } from './plugins/withRunOnce';\n\nexport { withDangerousMod } from './plugins/withDangerousMod';\nexport { withFinalizedMod } from './plugins/withFinalizedMod';\nexport { withMod, withBaseMod } from './plugins/withMod';\n\nexport {\n  withAppDelegate,\n  withInfoPlist,\n  withEntitlementsPlist,\n  withExpoPlist,\n  withXcodeProject,\n  withPodfile,\n  withPodfileProperties,\n} from './plugins/ios-plugins';\n\nexport {\n  withAndroidManifest,\n  withStringsXml,\n  withAndroidColors,\n  withAndroidColorsNight,\n  withAndroidStyles,\n  withMainActivity,\n  withMainApplication,\n  withProjectBuildGradle,\n  withAppBuildGradle,\n  withSettingsGradle,\n  withGradleProperties,\n} from './plugins/android-plugins';\n\nexport { withStaticPlugin } from './plugins/withStaticPlugin';\n\nexport { compileModsAsync, withDefaultBaseMods, evalModsAsync } from './plugins/mod-compiler';\n\nexport { PluginError } from './utils/errors';\n\nexport const BaseMods = {\n  withGeneratedBaseMods,\n  provider,\n  withAndroidBaseMods,\n  getAndroidModFileProviders,\n  withIosBaseMods,\n  getIosModFileProviders,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,SAAAA,cAAA;EAAA,MAAAC,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAH,aAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA2CG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAR,aAAA;EAAA;AAAA;AAC3C,SAAAS,UAAA;EAAA,MAAAR,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAM,SAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAmCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAC,SAAA;EAAA;AAAA;AACnC,SAAAC,eAAA;EAAA,MAAAT,IAAA,GAAAE,OAAA;EAAAO,cAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAU,qBAAA;EAAA,MAAAV,IAAA,GAAAE,OAAA;EAAAQ,oBAAA,YAAAA,CAAA;IAAA,OAAAV,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAW,iBAAA;EAAA,MAAAX,IAAA,GAAAE,OAAA;EAAAS,gBAAA,YAAAA,CAAA;IAAA,OAAAX,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAY,IAAA;EAAA,MAAAZ,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAU,GAAA,YAAAA,CAAA;IAAA,OAAAZ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAmCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAK,GAAA;EAAA;AAAA;AACnC,SAAAC,cAAA;EAAA,MAAAb,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAW,aAAA,YAAAA,CAAA;IAAA,OAAAb,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAsDG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAM,aAAA;EAAA;AAAA;AACtD,SAAAC,QAAA;EAAA,MAAAd,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAY,OAAA,YAAAA,CAAA;IAAA,OAAAd,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA2CG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAO,OAAA;EAAA;AAAA;AAC3C,SAAAC,kBAAA;EAAA,MAAAf,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAa,iBAAA,YAAAA,CAAA;IAAA,OAAAf,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAsDG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAQ,iBAAA;EAAA;AAAA;AAAA,SAAAC,SAAA;EAAA,MAAAhB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAc,QAAA,YAAAA,CAAA;IAAA,OAAAhB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAAG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAS,QAAA;EAAA;AAAA;AAatD,IAAAC,OAAA,GAAAf,OAAA;AAAAC,MAAA,CAAAe,IAAA,CAAAD,OAAA,EAAAE,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAjB,MAAA,CAAAkB,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAf,OAAA,IAAAA,OAAA,CAAAe,GAAA,MAAAH,OAAA,CAAAG,GAAA;EAAAjB,MAAA,CAAAC,cAAA,CAAAC,OAAA,EAAAe,GAAA;IAAAd,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAU,OAAA,CAAAG,GAAA;IAAA;EAAA;AAAA;AAEA,SAAAK,aAAA;EAAA,MAAAzB,IAAA,GAAAE,OAAA;EAAAuB,YAAA,YAAAA,CAAA;IAAA,OAAAzB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAA0B,aAAA;EAAA,MAAA1B,IAAA,GAAAE,OAAA;EAAAwB,YAAA,YAAAA,CAAA;IAAA,OAAA1B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAA2B,kBAAA;EAAA,MAAA3B,IAAA,GAAAE,OAAA;EAAAyB,iBAAA,YAAAA,CAAA;IAAA,OAAA3B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAA4B,kBAAA;EAAA,MAAA5B,IAAA,GAAAE,OAAA;EAAA0B,iBAAA,YAAAA,CAAA;IAAA,OAAA5B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAA6B,SAAA;EAAA,MAAA7B,IAAA,GAAAE,OAAA;EAAA2B,QAAA,YAAAA,CAAA;IAAA,OAAA7B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAA8B,YAAA;EAAA,MAAA9B,IAAA,GAAAE,OAAA;EAAA4B,WAAA,YAAAA,CAAA;IAAA,OAAA9B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAUA,SAAA+B,gBAAA;EAAA,MAAA/B,IAAA,GAAAE,OAAA;EAAA6B,eAAA,YAAAA,CAAA;IAAA,OAAA/B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAcA,SAAAgC,kBAAA;EAAA,MAAAhC,IAAA,GAAAE,OAAA;EAAA8B,iBAAA,YAAAA,CAAA;IAAA,OAAAhC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAiC,aAAA;EAAA,MAAAjC,IAAA,GAAAE,OAAA;EAAA+B,YAAA,YAAAA,CAAA;IAAA,OAAAjC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAkC,QAAA;EAAA,MAAAlC,IAAA,GAAAE,OAAA;EAAAgC,OAAA,YAAAA,CAAA;IAAA,OAAAlC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6C,SAAAmC,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAnC,wBAAAmC,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAhC,GAAA,CAAA6B,CAAA,OAAAO,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAA1C,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAA2C,wBAAA,WAAAC,CAAA,IAAAX,CAAA,oBAAAW,CAAA,OAAAzB,cAAA,CAAAC,IAAA,CAAAa,CAAA,EAAAW,CAAA,SAAAC,CAAA,GAAAH,CAAA,GAAA1C,MAAA,CAAA2C,wBAAA,CAAAV,CAAA,EAAAW,CAAA,UAAAC,CAAA,KAAAA,CAAA,CAAAzC,GAAA,IAAAyC,CAAA,CAAAC,GAAA,IAAA9C,MAAA,CAAAC,cAAA,CAAAuC,CAAA,EAAAI,CAAA,EAAAC,CAAA,IAAAL,CAAA,CAAAI,CAAA,IAAAX,CAAA,CAAAW,CAAA,YAAAJ,CAAA,CAAAF,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAU,GAAA,CAAAb,CAAA,EAAAO,CAAA,GAAAA,CAAA;AA9D7C;AACA;AACA;;AAWA;;AAOA;AACA;AACA;;AA0CO,MAAMO,QAAQ,GAAA7C,OAAA,CAAA6C,QAAA,GAAG;EACtBC,qBAAqB,EAArBA,sCAAqB;EACrBC,QAAQ,EAARA,yBAAQ;EACRC,mBAAmB,EAAnBA,0CAAmB;EACnBC,0BAA0B,EAA1BA,iDAA0B;EAC1BC,eAAe,EAAfA,kCAAe;EACfC,sBAAsB,EAAtBA;AACF,CAAC", "ignoreList": []}