{"version": 3, "file": "devtools.types.js", "sourceRoot": "", "sources": ["../src/devtools.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { WebSocketBackingStore } from './WebSocketBackingStore';\n\n/**\n * The connection info for devtools plugins client.\n */\nexport interface ConnectionInfo {\n  /** Indicates the sender towards the devtools plugin. */\n  sender:\n    | 'app' // client running in the app environment.\n    | 'browser'; // client running in the browser environment.\n\n  /** Dev server address. */\n  devServer: string;\n\n  /** The plugin name. */\n  pluginName: string;\n\n  /**\n   * The backing store for the WebSocket connection. Exposed for testing.\n   * If not provided, the default singleton instance will be used.\n   * @hidden\n   */\n  wsStore?: WebSocketBackingStore;\n\n  /**\n   * The transport protocol version between the app and the webui.\n   */\n  protocolVersion: number;\n}\n\n/**\n * Options for the devtools plugin client.\n */\nexport interface DevToolsPluginClientOptions {\n  /**\n   * The underlying WebSocket [`binaryType`](https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/binaryType).\n   */\n  websocketBinaryType?: 'arraybuffer' | 'blob';\n}\n\n/**\n * The handshake messages for the devtools plugin client.\n * @hidden\n */\nexport interface HandshakeMessageParams {\n  protocolVersion: number;\n  pluginName: string;\n  method: 'handshake' | 'terminateBrowserClient';\n  browserClientId: string;\n}\n"]}