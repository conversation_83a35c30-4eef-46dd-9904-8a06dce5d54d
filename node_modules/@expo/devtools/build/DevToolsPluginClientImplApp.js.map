{"version": 3, "file": "DevToolsPluginClientImplApp.js", "sourceRoot": "", "sources": ["../src/DevToolsPluginClientImplApp.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,KAAK,MAAM,MAAM,UAAU,CAAC;AAEnC;;GAEG;AACH,MAAM,OAAO,2BAA4B,SAAQ,oBAAoB;IACnE,uCAAuC;IAC/B,gBAAgB,GAA2B,EAAE,CAAC;IAEtD;;;OAGG;IACM,KAAK,CAAC,SAAS;QACtB,MAAM,KAAK,CAAC,SAAS,EAAE,CAAC;QACxB,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAEO,mBAAmB;QACzB,IAAI,CAAC,2BAA2B,CAAC,CAAC,MAAM,EAAE,EAAE;YAC1C,IAAI,MAAM,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;gBAClC,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,GAAG,MAAM,CAAC;gBAE/C,6BAA6B;gBAC7B,IAAI,eAAe,KAAK,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC;oBAC5D,6FAA6F;oBAC7F,OAAO,CAAC,IAAI,CACV,2EAA2E,UAAU,GAAG,CACzF,CAAC;oBACF,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC;oBAChE,OAAO;gBACT,CAAC;gBAED,+DAA+D;gBAC/D,MAAM,uBAAuB,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;gBAClE,IAAI,uBAAuB,IAAI,IAAI,IAAI,uBAAuB,KAAK,MAAM,CAAC,eAAe,EAAE,CAAC;oBAC1F,MAAM,CAAC,IAAI,CACT,8EAA8E,uBAAuB,GAAG,CACzG,CAAC;oBACF,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,uBAAuB,CAAC,CAAC;gBACnE,CAAC;gBACD,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC,eAAe,CAAC;YAC7D,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,sBAAsB,CAAC,UAAkB,EAAE,eAAuB;QACxE,IAAI,CAAC,oBAAoB,CAAC;YACxB,eAAe,EAAE,IAAI,CAAC,cAAc,CAAC,eAAe;YACpD,MAAM,EAAE,wBAAwB;YAChC,eAAe;YACf,UAAU;SACX,CAAC,CAAC;IACL,CAAC;CACF", "sourcesContent": ["import { DevToolsPluginClient } from './DevToolsPluginClient';\nimport * as logger from './logger';\n\n/**\n * The DevToolsPluginClient for the app -> browser communication.\n */\nexport class DevToolsPluginClientImplApp extends DevToolsPluginClient {\n  // Map of pluginName -> browserClientId\n  private browserClientMap: Record<string, string> = {};\n\n  /**\n   * Initialize the connection.\n   * @hidden\n   */\n  override async initAsync(): Promise<void> {\n    await super.initAsync();\n    this.addHandshakeHandler();\n  }\n\n  private addHandshakeHandler() {\n    this.addHandskakeMessageListener((params) => {\n      if (params.method === 'handshake') {\n        const { pluginName, protocolVersion } = params;\n\n        // [0] Check protocol version\n        if (protocolVersion !== this.connectionInfo.protocolVersion) {\n          // Use console.warn than logger because we want to show the warning even logging is disabled.\n          console.warn(\n            `Received an incompatible devtools plugin handshake message - pluginName[${pluginName}]`\n          );\n          this.terminateBrowserClient(pluginName, params.browserClientId);\n          return;\n        }\n\n        // [1] Terminate duplicated browser clients for the same plugin\n        const previousBrowserClientId = this.browserClientMap[pluginName];\n        if (previousBrowserClientId != null && previousBrowserClientId !== params.browserClientId) {\n          logger.info(\n            `Terminate the previous browser client connection - previousBrowserClientId[${previousBrowserClientId}]`\n          );\n          this.terminateBrowserClient(pluginName, previousBrowserClientId);\n        }\n        this.browserClientMap[pluginName] = params.browserClientId;\n      }\n    });\n  }\n\n  private terminateBrowserClient(pluginName: string, browserClientId: string) {\n    this.sendHandshakeMessage({\n      protocolVersion: this.connectionInfo.protocolVersion,\n      method: 'terminateBrowserClient',\n      browserClientId,\n      pluginName,\n    });\n  }\n}\n"]}