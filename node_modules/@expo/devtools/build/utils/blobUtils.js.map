{"version": 3, "file": "blobUtils.js", "sourceRoot": "", "sources": ["../../src/utils/blobUtils.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,UAAU,sBAAsB,CAAC,IAAU;IAC/C,IAAI,OAAO,IAAI,CAAC,WAAW,KAAK,UAAU,EAAE,CAAC;QAC3C,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;IAC5B,CAAC;IACD,OAAO,4BAA4B,CAAC,IAAI,CAAC,CAAC;AAC5C,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,4BAA4B,CAAC,IAAU;IAC3D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;QAChC,MAAM,CAAC,MAAM,GAAG,GAAG,EAAE;YACnB,OAAO,CAAC,MAAM,CAAC,MAAqB,CAAC,CAAC;QACxC,CAAC,CAAC;QACF,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC;QACxB,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["/**\n * Converts a Blob to an ArrayBuffer.\n */\nexport function blobToArrayBufferAsync(blob: Blob): Promise<ArrayBuffer> {\n  if (typeof blob.arrayBuffer === 'function') {\n    return blob.arrayBuffer();\n  }\n  return legacyBlobToArrayBufferAsync(blob);\n}\n\n/**\n * Converts a Blob to an ArrayBuffer using the FileReader API.\n */\nexport async function legacyBlobToArrayBufferAsync(blob: Blob): Promise<ArrayBuffer> {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.onload = () => {\n      resolve(reader.result as ArrayBuffer);\n    };\n    reader.onerror = reject;\n    reader.readAsArrayBuffer(blob);\n  });\n}\n"]}