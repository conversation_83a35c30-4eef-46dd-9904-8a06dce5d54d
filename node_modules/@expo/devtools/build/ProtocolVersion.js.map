{"version": 3, "file": "ProtocolVersion.js", "sourceRoot": "", "sources": ["../src/ProtocolVersion.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,CAAC", "sourcesContent": ["/**\n * A transport protocol version between the app and the webui.\n * It shows a warning in handshaking stage if the version is different between the app and the webui.\n * The value should be increased when the protocol is changed.\n */\nexport const PROTOCOL_VERSION = 1;\n"]}