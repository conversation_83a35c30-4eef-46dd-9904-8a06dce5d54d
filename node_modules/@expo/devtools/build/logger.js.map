{"version": 3, "file": "logger.js", "sourceRoot": "", "sources": ["../src/logger.ts"], "names": [], "mappings": "AAAA,IAAI,aAAa,GAAG,KAAK,CAAC;AAE1B,MAAM,UAAU,GAAG,CAAC,GAAG,MAAsC;IAC3D,IAAI,aAAa,EAAE,CAAC;QAClB,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;IACzB,CAAC;AACH,CAAC;AAED,MAAM,UAAU,KAAK,CAAC,GAAG,MAAwC;IAC/D,IAAI,aAAa,EAAE,CAAC;QAClB,OAAO,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC;IAC3B,CAAC;AACH,CAAC;AAED,MAAM,UAAU,IAAI,CAAC,GAAG,MAAuC;IAC7D,IAAI,aAAa,EAAE,CAAC;QAClB,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;IAC1B,CAAC;AACH,CAAC;AAED,MAAM,UAAU,IAAI,CAAC,GAAG,MAAuC;IAC7D,IAAI,aAAa,EAAE,CAAC;QAClB,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;IAC1B,CAAC;AACH,CAAC;AAED,MAAM,UAAU,gBAAgB,CAAC,OAAgB;IAC/C,aAAa,GAAG,OAAO,CAAC;AAC1B,CAAC", "sourcesContent": ["let enableLogging = false;\n\nexport function log(...params: Parameters<typeof console.log>) {\n  if (enableLogging) {\n    console.log(...params);\n  }\n}\n\nexport function debug(...params: Parameters<typeof console.debug>) {\n  if (enableLogging) {\n    console.debug(...params);\n  }\n}\n\nexport function info(...params: Parameters<typeof console.info>) {\n  if (enableLogging) {\n    console.info(...params);\n  }\n}\n\nexport function warn(...params: Parameters<typeof console.info>) {\n  if (enableLogging) {\n    console.warn(...params);\n  }\n}\n\nexport function setEnableLogging(enabled: boolean) {\n  enableLogging = enabled;\n}\n"]}