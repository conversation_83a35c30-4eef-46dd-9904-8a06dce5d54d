{"version": 3, "file": "WebSocketWithReconnect.js", "sourceRoot": "", "sources": ["../src/WebSocketWithReconnect.ts"], "names": [], "mappings": "AA4DA,MAAM,OAAO,sBAAsB;IAkBf;IAjBD,eAAe,CAAS;IACxB,UAAU,CAAS;IACnB,cAAc,CAAS;IACvB,OAAO,CAAyB;IAChC,WAAW,CAA2B;IAE/C,EAAE,GAAqB,IAAI,CAAC;IAC5B,OAAO,GAAG,CAAC,CAAC;IACZ,oBAAoB,GAAyC,IAAI,CAAC;IAClE,QAAQ,GAAG,KAAK,CAAC;IACjB,SAAS,GAA0D,EAAE,CAAC;IACtE,cAAc,GAAgE,IAAI,CAAC;IACnF,cAAc,CAAyB;IAE9B,YAAY,CAAyB;IAEtD,YACkB,GAAW,EAC3B,OAAiB;QADD,QAAG,GAAH,GAAG,CAAQ;QAG3B,IAAI,CAAC,eAAe,GAAG,OAAO,EAAE,eAAe,IAAI,IAAI,CAAC;QACxD,IAAI,CAAC,UAAU,GAAG,OAAO,EAAE,UAAU,IAAI,GAAG,CAAC;QAC7C,IAAI,CAAC,cAAc,GAAG,OAAO,EAAE,cAAc,IAAI,IAAI,CAAC;QACtD,IAAI,CAAC,OAAO;YACV,OAAO,EAAE,OAAO;gBAChB,CAAC,CAAC,KAAK,EAAE,EAAE;oBACT,MAAM,KAAK,CAAC;gBACd,CAAC,CAAC,CAAC;QACL,IAAI,CAAC,WAAW,GAAG,OAAO,EAAE,WAAW,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QACtD,IAAI,CAAC,YAAY,GAAG,OAAO,EAAE,UAAU,CAAC;QACxC,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAE1C,IAAI,CAAC,OAAO,EAAE,CAAC;IACjB,CAAC;IAEM,KAAK,CAAC,IAAa,EAAE,MAAe;QACzC,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACnC,IAAI,CAAC,SAAS,CACZ,OAAO,EACP,CAAC,IAAI,CAAC,cAAc,IAAI;YACtB,IAAI,EAAE,IAAI,IAAI,IAAI;YAClB,MAAM,EAAE,MAAM,IAAI,kBAAkB;YACpC,OAAO,EAAE,kBAAkB;SAC5B,CAAwB,CAC1B,CAAC;QACF,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC;YACpB,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;YACnB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;YACf,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACnB,CAAC;IACH,CAAC;IAMM,gBAAgB,CAAC,KAAa,EAAE,QAA8B;QACnE,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC;QACzF,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC1B,CAAC;IAEM,mBAAmB,CAAC,KAAa,EAAE,QAA8B;QACtE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAED,mBAAmB;IAEX,OAAO;QACb,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC;YACpB,OAAO;QACT,CAAC;QACD,IAAI,CAAC,oBAAoB,GAAG,UAAU,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAEvF,IAAI,CAAC,EAAE,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC7C,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,EAAE,CAAC;YAC9B,IAAI,CAAC,EAAE,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC;QACzC,CAAC;QACD,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QACxD,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAClD,+FAA+F;QAC/F,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QACpD,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IACtD,CAAC;IAEM,IAAI,CAAC,IAAuD;QACjE,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC,CAAC;YACpE,OAAO;QACT,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpC,IAAI,CAAC,OAAO,CACV,IAAI,KAAK,CAAC,uDAAuD,IAAI,CAAC,OAAO,GAAG,CAAC,CAClF,CAAC;YACF,OAAO;QACT,CAAC;QAED,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QACnB,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,CAAC,UAAU,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;YACnD,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;IAMO,SAAS,CAAC,KAAa,EAAE,OAAY;QAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAC7C,IAAI,SAAS,EAAE,CAAC;YACd,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACpB,CAAC;QACH,CAAC;IACH,CAAC;IAEO,UAAU,GAAG,GAAG,EAAE;QACxB,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACnC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAEvB,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClB,CAAC;IACH,CAAC,CAAC;IAEM,aAAa,GAAG,CAAC,KAA4B,EAAE,EAAE;QACvD,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IACnC,CAAC,CAAC;IAEM,WAAW,GAAG,CAAC,KAA0B,EAAE,EAAE;QACnD,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACnC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC/B,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;IAC/D,CAAC,CAAC;IAEM,WAAW,GAAG,CAAC,KAA0B,EAAE,EAAE;QACnD,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACnC,IAAI,CAAC,cAAc,GAAG;YACpB,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,MAAM,EAAE,KAAK,CAAC,MAAM;YACpB,OAAO,EAAE,KAAK,CAAC,OAAO;SACvB,CAAC;QACF,IAAI,CAAC,iBAAiB,CAAC,2BAA2B,KAAK,CAAC,IAAI,YAAY,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;IAC3F,CAAC,CAAC;IAEM,oBAAoB,GAAG,GAAG,EAAE;QAClC,IAAI,CAAC,iBAAiB,CAAC,0CAA0C,CAAC,CAAC;IACrE,CAAC,CAAC;IAEM,2BAA2B;QACjC,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,EAAE,CAAC;YACtC,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACxC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACnC,CAAC;IACH,CAAC;IAEO,iBAAiB,CAAC,MAAc;QACtC,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC;YACpB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACtB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;QACjB,CAAC;QACD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,OAAO;QACT,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpC,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,OAAO;QACT,CAAC;QAED,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC;YAClB,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC3B,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IAC3B,CAAC;IAEO,OAAO,CAAC,EAAa;QAC3B,IAAI,CAAC;YACH,EAAE,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YACtD,EAAE,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YAChD,EAAE,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YAElD,8DAA8D;YAC9D,2DAA2D;YAC3D,0FAA0F;YAC1F,oEAAoE;YACpE,EAAE,CAAC,OAAO,GAAG,GAAG,EAAE,GAAE,CAAC,CAAC;YAEtB,EAAE,CAAC,KAAK,EAAE,CAAC;QACb,CAAC;QAAC,MAAM,CAAC,CAAA,CAAC;IACZ,CAAC;IAED,IAAW,UAAU;QACnB,mFAAmF;QACnF,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,OAAO,SAAS,CAAC,MAAM,CAAC;QAC1B,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC;QACvC,IAAI,UAAU,KAAK,SAAS,CAAC,MAAM,EAAE,CAAC;YACpC,OAAO,SAAS,CAAC,UAAU,CAAC;QAC9B,CAAC;QACD,OAAO,UAAU,IAAI,SAAS,CAAC,UAAU,CAAC;IAC5C,CAAC;IAED,YAAY;IAEZ,6BAA6B;IAEb,UAAU,GAAG,CAAC,CAAC;IACf,IAAI,GAAG,CAAC,CAAC;IACT,OAAO,GAAG,CAAC,CAAC;IACZ,MAAM,GAAG,CAAC,CAAC;IAE3B,IAAW,UAAU;QACnB,OAAO,IAAI,CAAC,EAAE,EAAE,UAAU,IAAI,MAAM,CAAC;IACvC,CAAC;IAED,IAAW,cAAc;QACvB,OAAO,IAAI,CAAC,EAAE,EAAE,cAAc,IAAI,CAAC,CAAC;IACtC,CAAC;IAED,IAAW,UAAU;QACnB,OAAO,IAAI,CAAC,EAAE,EAAE,UAAU,IAAI,EAAE,CAAC;IACnC,CAAC;IAED,IAAW,QAAQ;QACjB,OAAO,IAAI,CAAC,EAAE,EAAE,QAAQ,IAAI,EAAE,CAAC;IACjC,CAAC;IAEM,IAAI;QACT,wDAAwD;QACxD,OAAO,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC;IACzB,CAAC;IAEM,aAAa,CAAC,KAAY;QAC/B,OAAO,IAAI,CAAC,EAAE,EAAE,aAAa,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC;IAChD,CAAC;IAED,YAAY;IAEZ,wCAAwC;IAExC,IAAW,OAAO,CAAC,MAAgD;QACjE,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;IAC/E,CAAC;IAED,IAAW,OAAO,CAAC,MAAkC;QACnD,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;IAC/E,CAAC;IAED,IAAW,SAAS,CAAC,MAAkD;QACrE,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;IAC/E,CAAC;IAED,IAAW,MAAM,CAAC,MAA0B;QAC1C,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;IAC/E,CAAC;CAGF", "sourcesContent": ["import type { DevToolsPluginClientOptions } from './devtools.types';\n\nexport interface WebSocketCloseEvent extends Event {\n  code?: number;\n  reason?: string;\n  // react-native WebSocket close event has additional message property\n  message?: string;\n}\nexport interface WebSocketErrorEvent extends Event {\n  // react-native WebSocket error event has additional message property\n  message?: string;\n}\nexport interface WebSocketMessageEvent extends Event {\n  data: string | ArrayBufferLike | Blob | ArrayBufferView;\n}\n\nexport interface Options {\n  /**\n   * Reconnect interval in milliseconds.\n   * @default 1500\n   */\n  retriesInterval?: number;\n\n  /**\n   * The maximum number of retries.\n   * @default 200\n   */\n  maxRetries?: number;\n\n  /**\n   * The timeout in milliseconds for the WebSocket connecting.\n   */\n  connectTimeout?: number;\n\n  /**\n   * The error handler.\n   * @default throwing an error\n   */\n  onError?: (error: Error) => void;\n\n  /**\n   * The callback to be called when the WebSocket is reconnected.\n   * @default no-op\n   */\n  onReconnect?: (reason: string) => void;\n\n  /**\n   * The [`binaryType`](https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/binaryType).\n   */\n  binaryType?: DevToolsPluginClientOptions['websocketBinaryType'];\n}\n\ninterface InternalEventListeners {\n  message?: Set<(event: WebSocketMessageEvent) => void>;\n  open?: Set<() => void>;\n  error?: Set<(event: WebSocketErrorEvent) => void>;\n  close?: Set<(event: WebSocketCloseEvent) => void>;\n  [eventName: string]: undefined | Set<(event: any) => void>;\n}\n\nexport class WebSocketWithReconnect implements WebSocket {\n  private readonly retriesInterval: number;\n  private readonly maxRetries: number;\n  private readonly connectTimeout: number;\n  private readonly onError: (error: Error) => void;\n  private readonly onReconnect: (reason: string) => void;\n\n  private ws: WebSocket | null = null;\n  private retries = 0;\n  private connectTimeoutHandle: ReturnType<typeof setTimeout> | null = null;\n  private isClosed = false;\n  private sendQueue: (string | ArrayBufferView | Blob | ArrayBufferLike)[] = [];\n  private lastCloseEvent: { code?: number; reason?: string; message?: string } | null = null;\n  private eventListeners: InternalEventListeners;\n\n  private readonly wsBinaryType?: Options['binaryType'];\n\n  constructor(\n    public readonly url: string,\n    options?: Options\n  ) {\n    this.retriesInterval = options?.retriesInterval ?? 1500;\n    this.maxRetries = options?.maxRetries ?? 200;\n    this.connectTimeout = options?.connectTimeout ?? 5000;\n    this.onError =\n      options?.onError ??\n      ((error) => {\n        throw error;\n      });\n    this.onReconnect = options?.onReconnect ?? (() => {});\n    this.wsBinaryType = options?.binaryType;\n    this.eventListeners = Object.create(null);\n\n    this.connect();\n  }\n\n  public close(code?: number, reason?: string) {\n    this.clearConnectTimeoutIfNeeded();\n    this.emitEvent(\n      'close',\n      (this.lastCloseEvent ?? {\n        code: code ?? 1000,\n        reason: reason ?? 'Explicit closing',\n        message: 'Explicit closing',\n      }) as WebSocketCloseEvent\n    );\n    this.lastCloseEvent = null;\n    this.isClosed = true;\n    this.eventListeners = Object.create(null);\n    this.sendQueue = [];\n    if (this.ws != null) {\n      const ws = this.ws;\n      this.ws = null;\n      this.wsClose(ws);\n    }\n  }\n\n  public addEventListener(event: 'message', listener: (event: WebSocketMessageEvent) => void): void;\n  public addEventListener(event: 'open', listener: () => void): void;\n  public addEventListener(event: 'error', listener: (event: WebSocketErrorEvent) => void): void;\n  public addEventListener(event: 'close', listener: (event: WebSocketCloseEvent) => void): void;\n  public addEventListener(event: string, listener: (event: any) => void) {\n    const listeners = this.eventListeners[event] || (this.eventListeners[event] = new Set());\n    listeners.add(listener);\n  }\n\n  public removeEventListener(event: string, listener: (event: any) => void) {\n    this.eventListeners[event]?.delete(listener);\n  }\n\n  //#region Internals\n\n  private connect() {\n    if (this.ws != null) {\n      return;\n    }\n    this.connectTimeoutHandle = setTimeout(this.handleConnectTimeout, this.connectTimeout);\n\n    this.ws = new WebSocket(this.url.toString());\n    if (this.wsBinaryType != null) {\n      this.ws.binaryType = this.wsBinaryType;\n    }\n    this.ws.addEventListener('message', this.handleMessage);\n    this.ws.addEventListener('open', this.handleOpen);\n    // @ts-ignore TypeScript expects (e: Event) => any, but we want (e: WebSocketErrorEvent) => any\n    this.ws.addEventListener('error', this.handleError);\n    this.ws.addEventListener('close', this.handleClose);\n  }\n\n  public send(data: string | ArrayBufferView | Blob | ArrayBufferLike): void {\n    if (this.isClosed) {\n      this.onError(new Error('Unable to send data: WebSocket is closed'));\n      return;\n    }\n\n    if (this.retries >= this.maxRetries) {\n      this.onError(\n        new Error(`Unable to send data: Exceeded max retries - retries[${this.retries}]`)\n      );\n      return;\n    }\n\n    const ws = this.ws;\n    if (ws != null && ws.readyState === WebSocket.OPEN) {\n      ws.send(data);\n    } else {\n      this.sendQueue.push(data);\n    }\n  }\n\n  private emitEvent(event: 'message', payload: WebSocketMessageEvent): void;\n  private emitEvent(event: 'open', payload?: void): void;\n  private emitEvent(event: 'error', payload: WebSocketErrorEvent): void;\n  private emitEvent(event: 'close', payload: WebSocketCloseEvent): void;\n  private emitEvent(event: string, payload: any) {\n    const listeners = this.eventListeners[event];\n    if (listeners) {\n      for (const listener of listeners) {\n        listener(payload);\n      }\n    }\n  }\n\n  private handleOpen = () => {\n    this.clearConnectTimeoutIfNeeded();\n    this.lastCloseEvent = null;\n    this.emitEvent('open');\n\n    const sendQueue = this.sendQueue;\n    this.sendQueue = [];\n    for (const data of sendQueue) {\n      this.send(data);\n    }\n  };\n\n  private handleMessage = (event: WebSocketMessageEvent) => {\n    this.emitEvent('message', event);\n  };\n\n  private handleError = (event: WebSocketErrorEvent) => {\n    this.clearConnectTimeoutIfNeeded();\n    this.emitEvent('error', event);\n    this.reconnectIfNeeded(`WebSocket error - ${event.message}`);\n  };\n\n  private handleClose = (event: WebSocketCloseEvent) => {\n    this.clearConnectTimeoutIfNeeded();\n    this.lastCloseEvent = {\n      code: event.code,\n      reason: event.reason,\n      message: event.message,\n    };\n    this.reconnectIfNeeded(`WebSocket closed - code[${event.code}] reason[${event.reason}]`);\n  };\n\n  private handleConnectTimeout = () => {\n    this.reconnectIfNeeded('Timeout from connecting to the WebSocket');\n  };\n\n  private clearConnectTimeoutIfNeeded() {\n    if (this.connectTimeoutHandle != null) {\n      clearTimeout(this.connectTimeoutHandle);\n      this.connectTimeoutHandle = null;\n    }\n  }\n\n  private reconnectIfNeeded(reason: string) {\n    if (this.ws != null) {\n      this.wsClose(this.ws);\n      this.ws = null;\n    }\n    if (this.isClosed) {\n      return;\n    }\n\n    if (this.retries >= this.maxRetries) {\n      this.onError(new Error('Exceeded max retries'));\n      this.close();\n      return;\n    }\n\n    setTimeout(() => {\n      this.retries += 1;\n      this.connect();\n      this.onReconnect(reason);\n    }, this.retriesInterval);\n  }\n\n  private wsClose(ws: WebSocket) {\n    try {\n      ws.removeEventListener('message', this.handleMessage);\n      ws.removeEventListener('open', this.handleOpen);\n      ws.removeEventListener('close', this.handleClose);\n\n      // WebSocket throws errors if we don't handle the error event.\n      // Specifically when closing a ws in CONNECTING readyState,\n      // WebSocket will have `WebSocket was closed before the connection was established` error.\n      // We won't like to have the exception, so set a noop error handler.\n      ws.onerror = () => {};\n\n      ws.close();\n    } catch {}\n  }\n\n  public get readyState() {\n    // Only return closed if the WebSocket is explicitly closed or exceeds max retries.\n    if (this.isClosed) {\n      return WebSocket.CLOSED;\n    }\n\n    const readyState = this.ws?.readyState;\n    if (readyState === WebSocket.CLOSED) {\n      return WebSocket.CONNECTING;\n    }\n    return readyState ?? WebSocket.CONNECTING;\n  }\n\n  //#endregion\n\n  //#region WebSocket API proxy\n\n  public readonly CONNECTING = 0;\n  public readonly OPEN = 1;\n  public readonly CLOSING = 2;\n  public readonly CLOSED = 3;\n\n  public get binaryType() {\n    return this.ws?.binaryType ?? 'blob';\n  }\n\n  public get bufferedAmount() {\n    return this.ws?.bufferedAmount ?? 0;\n  }\n\n  public get extensions() {\n    return this.ws?.extensions ?? '';\n  }\n\n  public get protocol() {\n    return this.ws?.protocol ?? '';\n  }\n\n  public ping(): void {\n    // @ts-ignore react-native WebSocket has the ping method\n    return this.ws?.ping();\n  }\n\n  public dispatchEvent(event: Event) {\n    return this.ws?.dispatchEvent(event) ?? false;\n  }\n\n  //#endregion\n\n  //#regions Unsupported legacy properties\n\n  public set onclose(_value: ((e: WebSocketCloseEvent) => any) | null) {\n    throw new Error('Unsupported legacy property, use addEventListener instead');\n  }\n\n  public set onerror(_value: ((e: Event) => any) | null) {\n    throw new Error('Unsupported legacy property, use addEventListener instead');\n  }\n\n  public set onmessage(_value: ((e: WebSocketMessageEvent) => any) | null) {\n    throw new Error('Unsupported legacy property, use addEventListener instead');\n  }\n\n  public set onopen(_value: (() => any) | null) {\n    throw new Error('Unsupported legacy property, use addEventListener instead');\n  }\n\n  //#endregion\n}\n"]}