{"version": 3, "file": "getConnectionInfo.js", "sourceRoot": "", "sources": ["../src/getConnectionInfo.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,gBAAgB,EAAE,MAAM,mBAAmB,CAAC;AAGrD,MAAM,UAAU,iBAAiB;IAC/B,MAAM,cAAc,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IACpF,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;IAChE,OAAO;QACL,eAAe,EAAE,gBAAgB;QACjC,MAAM,EAAE,SAAS;QACjB,SAAS,EAAE,cAAc,IAAI,IAAI;KAClC,CAAC;AACJ,CAAC", "sourcesContent": ["/**\n * Get the dev server address.\n */\n\nimport { PROTOCOL_VERSION } from './ProtocolVersion';\nimport type { ConnectionInfo } from './devtools.types';\n\nexport function getConnectionInfo(): Omit<ConnectionInfo, 'pluginName'> {\n  const devServerQuery = new URLSearchParams(window.location.search).get('devServer');\n  const host = window.location.origin.replace(/^https?:\\/\\//, '');\n  return {\n    protocolVersion: PROTOCOL_VERSION,\n    sender: 'browser',\n    devServer: devServerQuery || host,\n  };\n}\n"]}