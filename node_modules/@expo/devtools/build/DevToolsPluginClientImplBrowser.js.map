{"version": 3, "file": "DevToolsPluginClientImplBrowser.js", "sourceRoot": "", "sources": ["../src/DevToolsPluginClientImplBrowser.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,KAAK,MAAM,MAAM,UAAU,CAAC;AAEnC;;GAEG;AACH,MAAM,OAAO,+BAAgC,SAAQ,oBAAoB;IAC/D,eAAe,GAAW,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;IAExD;;;OAGG;IACM,KAAK,CAAC,SAAS;QACtB,MAAM,KAAK,CAAC,SAAS,EAAE,CAAC;QACxB,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAEO,cAAc;QACpB,IAAI,CAAC,2BAA2B,CAAC,CAAC,MAAM,EAAE,EAAE;YAC1C,IACE,MAAM,CAAC,MAAM,KAAK,wBAAwB;gBAC1C,IAAI,CAAC,eAAe,KAAK,MAAM,CAAC,eAAe,EAC/C,CAAC;gBACD,MAAM,CAAC,IAAI,CACT,+EAA+E,CAChF,CAAC;gBACF,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,CAAC;QACH,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,oBAAoB,CAAC;YACxB,eAAe,EAAE,IAAI,CAAC,cAAc,CAAC,eAAe;YACpD,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,UAAU;YAC1C,MAAM,EAAE,WAAW;YACnB,eAAe,EAAE,IAAI,CAAC,eAAe;SACtC,CAAC,CAAC;IACL,CAAC;CACF", "sourcesContent": ["import { DevToolsPluginClient } from './DevToolsPluginClient';\nimport * as logger from './logger';\n\n/**\n * The DevToolsPluginClient for the browser -> app communication.\n */\nexport class DevToolsPluginClientImplBrowser extends DevToolsPluginClient {\n  private browserClientId: string = Date.now().toString();\n\n  /**\n   * Initialize the connection.\n   * @hidden\n   */\n  override async initAsync(): Promise<void> {\n    await super.initAsync();\n    this.startHandshake();\n  }\n\n  private startHandshake() {\n    this.addHandskakeMessageListener((params) => {\n      if (\n        params.method === 'terminateBrowserClient' &&\n        this.browserClientId === params.browserClientId\n      ) {\n        logger.info(\n          'Received terminateBrowserClient messages and terminate the current connection'\n        );\n        this.closeAsync();\n      }\n    });\n    this.sendHandshakeMessage({\n      protocolVersion: this.connectionInfo.protocolVersion,\n      pluginName: this.connectionInfo.pluginName,\n      method: 'handshake',\n      browserClientId: this.browserClientId,\n    });\n  }\n}\n"]}