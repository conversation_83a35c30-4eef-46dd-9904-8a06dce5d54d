{"version": 3, "file": "hooks.js", "sourceRoot": "", "sources": ["../src/hooks.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AAG5C,OAAO,EAAE,4BAA4B,EAAE,MAAM,+BAA+B,CAAC;AAG7E;;GAEG;AACH,MAAM,UAAU,uBAAuB,CACrC,UAAkB,EAClB,OAAqC;IAErC,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,QAAQ,CAA8B,IAAI,CAAC,CAAC;IACxE,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAe,IAAI,CAAC,CAAC;IAEvD,SAAS,CAAC,GAAG,EAAE;QACb,KAAK,UAAU,KAAK;YAClB,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,4BAA4B,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;gBACvE,SAAS,CAAC,MAAM,CAAC,CAAC;YACpB,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,QAAQ,CAAC,IAAI,KAAK,CAAC,uDAAuD,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YAC9F,CAAC;QACH,CAAC;QAED,KAAK,UAAU,QAAQ;YACrB,IAAI,CAAC;gBACH,MAAM,MAAM,EAAE,UAAU,EAAE,CAAC;YAC7B,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,QAAQ,CACN,IAAI,KAAK,CAAC,0DAA0D,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CACrF,CAAC;YACJ,CAAC;QACH,CAAC;QAED,KAAK,EAAE,CAAC;QACR,OAAO,GAAG,EAAE;YACV,QAAQ,EAAE,CAAC;QACb,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;IAEjB,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;QAClB,MAAM,KAAK,CAAC;IACd,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC", "sourcesContent": ["import { useState, useEffect } from 'react';\n\nimport { DevToolsPluginClient } from './DevToolsPluginClient';\nimport { getDevToolsPluginClientAsync } from './DevToolsPluginClientFactory';\nimport type { DevToolsPluginClientOptions } from './devtools.types';\n\n/**\n * A React hook to get the DevToolsPluginClient instance.\n */\nexport function useDevToolsPluginClient(\n  pluginName: string,\n  options?: DevToolsPluginClientOptions\n): DevToolsPluginClient | null {\n  const [client, setClient] = useState<DevToolsPluginClient | null>(null);\n  const [error, setError] = useState<Error | null>(null);\n\n  useEffect(() => {\n    async function setup() {\n      try {\n        const client = await getDevToolsPluginClientAsync(pluginName, options);\n        setClient(client);\n      } catch (e: any) {\n        setError(new Error('Failed to setup client from useDevToolsPluginClient: ' + e.toString()));\n      }\n    }\n\n    async function teardown() {\n      try {\n        await client?.closeAsync();\n      } catch (e: any) {\n        setError(\n          new Error('Failed to teardown client from useDevToolsPluginClient: ' + e.toString())\n        );\n      }\n    }\n\n    setup();\n    return () => {\n      teardown();\n    };\n  }, [pluginName]);\n\n  if (error != null) {\n    throw error;\n  }\n  return client;\n}\n"]}