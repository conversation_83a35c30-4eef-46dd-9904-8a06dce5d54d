{"version": 3, "file": "MessageFramePacker.js", "sourceRoot": "", "sources": ["../src/MessageFramePacker.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8BG;AAEH,OAAO,EAAE,sBAAsB,EAAE,MAAM,mBAAmB,CAAC;AAK3D,IAAK,oBASJ;AATD,WAAK,oBAAoB;IACvB,2EAAc,CAAA;IACd,mEAAU,CAAA;IACV,mEAAU,CAAA;IACV,+DAAQ,CAAA;IACR,yEAAa,CAAA;IACb,mEAAU,CAAA;IACV,6EAAe,CAAA;IACf,+DAAQ,CAAA;AACV,CAAC,EATI,oBAAoB,KAApB,oBAAoB,QASxB;AAOD,MAAM,OAAO,kBAAkB;IACrB,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC;IAChC,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC;IAEjC,IAAI,CAAC,EAAE,UAAU,EAAE,OAAO,EAAmB;QAClD,8DAA8D;QAC9D,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC,CAAC;QACjD,CAAC;QAED,2CAA2C;QAC3C,IAAI,OAAO,YAAY,IAAI,EAAE,CAAC;YAC5B,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE;gBAC3C,IAAI,CAAC;oBACH,MAAM,WAAW,GAAG,MAAM,sBAAsB,CAAC,OAAO,CAAC,CAAC;oBAC1D,OAAO,CACL,IAAI,CAAC,QAAQ,CACX,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,UAAU,CAAC,WAAW,CAAC,EAAE,EACpD,oBAAoB,CAAC,IAAI,CAC1B,CACF,CAAC;gBACJ,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,CAAC,KAAK,CAAC,CAAC;gBAChB,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,kDAAkD;QAClD,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,EAAE,SAAS,CAAC,CAAC;IAC3D,CAAC;IAEM,MAAM,CAAC,UAAgC;QAC5C,4DAA4D;QAC5D,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAChC,CAAC;QAED,iCAAiC;QACjC,MAAM,oBAAoB,GAAG,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5D,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAElE,mCAAmC;QACnC,MAAM,eAAe,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,CAAC;QAClE,MAAM,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;QAClE,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;QAEhD,oCAAoC;QACpC,MAAM,eAAe,GAAG,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC,CAAC,CAAC;QAC1E,MAAM,WAAW,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAEhD,gCAAgC;QAChC,MAAM,aAAa,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,gBAAgB,GAAG,CAAC,CAAC,CAAC;QACjE,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QAEpE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC;IACjC,CAAC;IAEO,iBAAiB,CAAC,OAAoB;QAC5C,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;YACpB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAM,WAAW,GAAG,OAAO,OAAO,CAAC;QACnC,IAAI,WAAW,KAAK,QAAQ,IAAI,WAAW,KAAK,QAAQ,EAAE,CAAC;YACzD,OAAO,IAAI,CAAC;QACd,CAAC;QACD,IAAI,WAAW,KAAK,QAAQ,IAAI,OAAO,CAAC,WAAW,KAAK,MAAM,EAAE,CAAC;YAC/D,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,mBAAmB,CAAC,OAAoB;QAC9C,IAAI,OAAO,YAAY,UAAU,EAAE,CAAC;YAClC,OAAO,OAAO,CAAC;QACjB,CAAC;aAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YACvC,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC1C,CAAC;aAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YACvC,MAAM,MAAM,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC;YAClC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;YACnC,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;QAChC,CAAC;aAAM,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;YAC5B,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC;aAAM,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YACjC,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC;aAAM,IAAI,OAAO,YAAY,WAAW,EAAE,CAAC;YAC1C,OAAO,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC;QACjC,CAAC;aAAM,IAAI,OAAO,YAAY,IAAI,EAAE,CAAC;YACnC,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAEO,QAAQ,CACd,EAAE,UAAU,EAAE,OAAO,EAAmB,EACxC,WAA6C;QAE7C,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QACpD,MAAM,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;QAClE,MAAM,gBAAgB,GAAG,eAAe,CAAC,MAAM,CAAC;QAChD,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAExD,MAAM,WAAW,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC,GAAG,aAAa,CAAC,UAAU,CAAC;QACxE,MAAM,MAAM,GAAG,IAAI,WAAW,CAAC,WAAW,CAAC,CAAC;QAC5C,MAAM,WAAW,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;QAE3C,iCAAiC;QACjC,MAAM,oBAAoB,GAAG,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACxD,oBAAoB,CAAC,SAAS,CAAC,CAAC,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;QAE3D,mCAAmC;QACnC,WAAW,CAAC,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QAEpC,oCAAoC;QACpC,MAAM,eAAe,GAAG,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACtE,eAAe,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW,IAAI,kBAAkB,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC,CAAC;QAEhG,gCAAgC;QAChC,WAAW,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,GAAG,gBAAgB,GAAG,CAAC,CAAC,CAAC;QAEzD,OAAO,WAAW,CAAC;IACrB,CAAC;IAEO,kBAAkB,CACxB,aAA0B,EAC1B,oBAA0C;QAE1C,QAAQ,oBAAoB,EAAE,CAAC;YAC7B,KAAK,oBAAoB,CAAC,UAAU,CAAC,CAAC,CAAC;gBACrC,OAAO,IAAI,UAAU,CAAC,aAAa,CAAC,CAAC;YACvC,CAAC;YACD,KAAK,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC;gBACjC,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAChD,CAAC;YACD,KAAK,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC;gBACjC,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,aAAa,CAAC,CAAC;gBACzC,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;YACnC,CAAC;YACD,KAAK,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC/B,OAAO,IAAI,CAAC;YACd,CAAC;YACD,KAAK,oBAAoB,CAAC,SAAS,CAAC,CAAC,CAAC;gBACpC,OAAO,SAAS,CAAC;YACnB,CAAC;YACD,KAAK,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC;gBACjC,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;gBAC1D,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAChC,CAAC;YACD,KAAK,oBAAoB,CAAC,WAAW,CAAC,CAAC,CAAC;gBACtC,OAAO,aAAa,CAAC;YACvB,CAAC;YACD,KAAK,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC/B,OAAO,IAAI,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC;YACnC,CAAC;YACD;gBACE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,uBAAuB,CAAC,OAAoB;QACzD,IAAI,OAAO,YAAY,UAAU,EAAE,CAAC;YAClC,OAAO,oBAAoB,CAAC,UAAU,CAAC;QACzC,CAAC;aAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YACvC,OAAO,oBAAoB,CAAC,MAAM,CAAC;QACrC,CAAC;aAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YACvC,OAAO,oBAAoB,CAAC,MAAM,CAAC;QACrC,CAAC;aAAM,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;YAC5B,OAAO,oBAAoB,CAAC,IAAI,CAAC;QACnC,CAAC;aAAM,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YACjC,OAAO,oBAAoB,CAAC,SAAS,CAAC;QACxC,CAAC;aAAM,IAAI,OAAO,YAAY,WAAW,EAAE,CAAC;YAC1C,OAAO,oBAAoB,CAAC,WAAW,CAAC;QAC1C,CAAC;aAAM,IAAI,OAAO,YAAY,IAAI,EAAE,CAAC;YACnC,OAAO,oBAAoB,CAAC,IAAI,CAAC;QACnC,CAAC;aAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YACvC,OAAO,oBAAoB,CAAC,MAAM,CAAC;QACrC,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;CACF", "sourcesContent": ["/**\n * A message frame packer that serializes a messageKey and a payload into either a JSON string\n * (fast path) or a binary format (for complex payloads).\n *\n * Fast Path (JSON.stringify/JSON.parse):\n * - For simple payloads (e.g., strings, numbers, null, undefined, or plain objects), the packer\n *   uses `JSON.stringify` for serialization and `JSON.parse` for deserialization, ensuring\n *   optimal performance.\n *\n * Binary Format:\n * - For more complex payloads (e.g., Uint8Array, ArrayBuffer, Blob), the packer uses a binary\n *   format with the following structure:\n *\n *   +------------------+-------------------+----------------------------+--------------------------+\n *   | 4 bytes (Uint32) | Variable length   | 1 byte (Uint8)             | Variable length          |\n *   | MessageKeyLength | MessageKey (JSON) | PayloadTypeIndicator (enum)| Payload (binary data)    |\n *   +------------------+-------------------+----------------------------+--------------------------+\n *\n *   1. MessageKeyLength (4 bytes):\n *      - A 4-byte unsigned integer indicating the length of the MessageKey JSON string.\n *\n *   2. MessageKey (Variable length):\n *      - The JSON string representing the message key, encoded as UTF-8.\n *\n *   3. PayloadTypeIndicator (1 byte):\n *      - A single byte enum value representing the type of the payload (e.g., Uint8Array, String,\n *        Object, ArrayBuffer, Blob).\n *\n *   4. Payload (Variable length):\n *      - The actual payload data, which can vary in type and length depending on the PayloadType.\n */\n\nimport { blobToArrayBufferAsync } from './utils/blobUtils';\n\ntype MessageKeyTypeBase = string | object;\ntype PayloadType = Uint8Array | string | number | null | undefined | object | ArrayBuffer | Blob;\n\nenum PayloadTypeIndicator {\n  Uint8Array = 1,\n  String = 2,\n  Number = 3,\n  Null = 4,\n  Undefined = 5,\n  Object = 6,\n  ArrayBuffer = 7,\n  Blob = 8,\n}\n\ninterface MessageFrame<T extends MessageKeyTypeBase> {\n  messageKey: T;\n  payload?: PayloadType;\n}\n\nexport class MessageFramePacker<T extends MessageKeyTypeBase> {\n  private textEncoder = new TextEncoder();\n  private textDecoder = new TextDecoder();\n\n  public pack({ messageKey, payload }: MessageFrame<T>): string | Uint8Array | Promise<Uint8Array> {\n    // Fast path to pack as string given `JSON.stringify` is fast.\n    if (this.isFastPathPayload(payload)) {\n      return JSON.stringify({ messageKey, payload });\n    }\n\n    // Slowest path for Blob returns a promise.\n    if (payload instanceof Blob) {\n      return new Promise(async (resolve, reject) => {\n        try {\n          const arrayBuffer = await blobToArrayBufferAsync(payload);\n          resolve(\n            this.packImpl(\n              { messageKey, payload: new Uint8Array(arrayBuffer) },\n              PayloadTypeIndicator.Blob\n            )\n          );\n        } catch (error) {\n          reject(error);\n        }\n      });\n    }\n\n    // Slow path for other types returns a Uint8Array.\n    return this.packImpl({ messageKey, payload }, undefined);\n  }\n\n  public unpack(packedData: string | ArrayBuffer): MessageFrame<T> {\n    // Fast path to unpack as string given `JSON.parse` is fast.\n    if (typeof packedData === 'string') {\n      return JSON.parse(packedData);\n    }\n\n    // [0] messageKeyLength (4 bytes)\n    const messageKeyLengthView = new DataView(packedData, 0, 4);\n    const messageKeyLength = messageKeyLengthView.getUint32(0, false);\n\n    // [1] messageKey (variable length)\n    const messageKeyBytes = packedData.slice(4, 4 + messageKeyLength);\n    const messageKeyString = this.textDecoder.decode(messageKeyBytes);\n    const messageKey = JSON.parse(messageKeyString);\n\n    // [2] payloadTypeIndicator (1 byte)\n    const payloadTypeView = new DataView(packedData, 4 + messageKeyLength, 1);\n    const payloadType = payloadTypeView.getUint8(0);\n\n    // [3] payload (variable length)\n    const payloadBinary = packedData.slice(4 + messageKeyLength + 1);\n    const payload = this.deserializePayload(payloadBinary, payloadType);\n\n    return { messageKey, payload };\n  }\n\n  private isFastPathPayload(payload: PayloadType): boolean {\n    if (payload == null) {\n      return true;\n    }\n    const payloadType = typeof payload;\n    if (payloadType === 'string' || payloadType === 'number') {\n      return true;\n    }\n    if (payloadType === 'object' && payload.constructor === Object) {\n      return true;\n    }\n    return false;\n  }\n\n  private payloadToUint8Array(payload: PayloadType): Uint8Array {\n    if (payload instanceof Uint8Array) {\n      return payload;\n    } else if (typeof payload === 'string') {\n      return this.textEncoder.encode(payload);\n    } else if (typeof payload === 'number') {\n      const buffer = new ArrayBuffer(8);\n      const view = new DataView(buffer);\n      view.setFloat64(0, payload, false);\n      return new Uint8Array(buffer);\n    } else if (payload === null) {\n      return new Uint8Array(0);\n    } else if (payload === undefined) {\n      return new Uint8Array(0);\n    } else if (payload instanceof ArrayBuffer) {\n      return new Uint8Array(payload);\n    } else if (payload instanceof Blob) {\n      throw new Error('Blob is not supported in this callsite.');\n    } else {\n      return this.textEncoder.encode(JSON.stringify(payload));\n    }\n  }\n\n  private packImpl(\n    { messageKey, payload }: MessageFrame<T>,\n    payloadType: PayloadTypeIndicator | undefined\n  ): Promise<Uint8Array> | Uint8Array {\n    const messageKeyString = JSON.stringify(messageKey);\n    const messageKeyBytes = this.textEncoder.encode(messageKeyString);\n    const messageKeyLength = messageKeyBytes.length;\n    const payloadBinary = this.payloadToUint8Array(payload);\n\n    const totalLength = 4 + messageKeyLength + 1 + payloadBinary.byteLength;\n    const buffer = new ArrayBuffer(totalLength);\n    const packedArray = new Uint8Array(buffer);\n\n    // [0] messageKeyLength (4 bytes)\n    const messageKeyLengthView = new DataView(buffer, 0, 4);\n    messageKeyLengthView.setUint32(0, messageKeyLength, false);\n\n    // [1] messageKey (variable length)\n    packedArray.set(messageKeyBytes, 4);\n\n    // [2] payloadTypeIndicator (1 byte)\n    const payloadTypeView = new DataView(buffer, 4 + messageKeyLength, 1);\n    payloadTypeView.setUint8(0, payloadType ?? MessageFramePacker.getPayloadTypeIndicator(payload));\n\n    // [3] payload (variable length)\n    packedArray.set(payloadBinary, 4 + messageKeyLength + 1);\n\n    return packedArray;\n  }\n\n  private deserializePayload(\n    payloadBinary: ArrayBuffer,\n    payloadTypeIndicator: PayloadTypeIndicator\n  ): PayloadType {\n    switch (payloadTypeIndicator) {\n      case PayloadTypeIndicator.Uint8Array: {\n        return new Uint8Array(payloadBinary);\n      }\n      case PayloadTypeIndicator.String: {\n        return this.textDecoder.decode(payloadBinary);\n      }\n      case PayloadTypeIndicator.Number: {\n        const view = new DataView(payloadBinary);\n        return view.getFloat64(0, false);\n      }\n      case PayloadTypeIndicator.Null: {\n        return null;\n      }\n      case PayloadTypeIndicator.Undefined: {\n        return undefined;\n      }\n      case PayloadTypeIndicator.Object: {\n        const jsonString = this.textDecoder.decode(payloadBinary);\n        return JSON.parse(jsonString);\n      }\n      case PayloadTypeIndicator.ArrayBuffer: {\n        return payloadBinary;\n      }\n      case PayloadTypeIndicator.Blob: {\n        return new Blob([payloadBinary]);\n      }\n      default:\n        throw new Error('Unsupported payload type');\n    }\n  }\n\n  private static getPayloadTypeIndicator(payload: PayloadType): PayloadTypeIndicator {\n    if (payload instanceof Uint8Array) {\n      return PayloadTypeIndicator.Uint8Array;\n    } else if (typeof payload === 'string') {\n      return PayloadTypeIndicator.String;\n    } else if (typeof payload === 'number') {\n      return PayloadTypeIndicator.Number;\n    } else if (payload === null) {\n      return PayloadTypeIndicator.Null;\n    } else if (payload === undefined) {\n      return PayloadTypeIndicator.Undefined;\n    } else if (payload instanceof ArrayBuffer) {\n      return PayloadTypeIndicator.ArrayBuffer;\n    } else if (payload instanceof Blob) {\n      return PayloadTypeIndicator.Blob;\n    } else if (typeof payload === 'object') {\n      return PayloadTypeIndicator.Object;\n    } else {\n      throw new Error('Unsupported payload type');\n    }\n  }\n}\n"]}