{"version": 3, "file": "WebSocketBackingStore.js", "sourceRoot": "", "sources": ["../src/WebSocketBackingStore.ts"], "names": [], "mappings": "AAAA;;;GAGG;AACH,MAAM,OAAO,qBAAqB;IAEvB;IACA;IAFT,YACS,KAAuB,IAAI,EAC3B,WAAmB,CAAC;QADpB,OAAE,GAAF,EAAE,CAAyB;QAC3B,aAAQ,GAAR,QAAQ,CAAY;IAC1B,CAAC;CACL", "sourcesContent": ["/**\n * The backing store for the WebSocket connection and reference count.\n * This is used for connection multiplexing.\n */\nexport class WebSocketBackingStore {\n  constructor(\n    public ws: WebSocket | null = null,\n    public refCount: number = 0\n  ) {}\n}\n"]}