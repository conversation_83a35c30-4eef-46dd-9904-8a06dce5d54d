{"version": 3, "file": "DevToolsPluginClientFactory.js", "sourceRoot": "", "sources": ["../src/DevToolsPluginClientFactory.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,2BAA2B,EAAE,MAAM,+BAA+B,CAAC;AAC5E,OAAO,EAAE,+BAA+B,EAAE,MAAM,mCAAmC,CAAC;AAEpF,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AAExD,MAAM,WAAW,GAAyE,EAAE,CAAC;AAE7F;;;GAGG;AACH,MAAM,CAAC,KAAK,UAAU,0BAA0B,CAC9C,cAA8B,EAC9B,OAAqC;IAErC,IAAI,MAA4B,CAAC;IACjC,IAAI,cAAc,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;QACpC,MAAM,GAAG,IAAI,2BAA2B,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;SAAM,CAAC;QACN,MAAM,GAAG,IAAI,+BAA+B,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;IACxE,CAAC;IACD,MAAM,MAAM,CAAC,SAAS,EAAE,CAAC;IACzB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,4BAA4B,CAChD,UAAkB,EAClB,OAAqC;IAErC,MAAM,cAAc,GAAG,iBAAiB,EAAE,CAAC;IAE3C,IAAI,QAAQ,GACV,WAAW,CAAC,UAAU,CAAC,CAAC;IAC1B,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;QACrB,IAAI,QAAQ,YAAY,OAAO,EAAE,CAAC;YAChC,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,IACE,QAAQ,CAAC,WAAW,EAAE,KAAK,KAAK;YAChC,QAAQ,CAAC,cAAc,CAAC,SAAS,KAAK,cAAc,CAAC,SAAS,EAC9D,CAAC;YACD,MAAM,QAAQ,CAAC,UAAU,EAAE,CAAC;YAC5B,OAAO,WAAW,CAAC,UAAU,CAAC,CAAC;YAC/B,QAAQ,GAAG,IAAI,CAAC;QAClB,CAAC;IACH,CAAC;IACD,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;QACrB,MAAM,eAAe,GAAG,0BAA0B,CAAC,EAAE,GAAG,cAAc,EAAE,UAAU,EAAE,EAAE,OAAO,CAAC,CAAC;QAC/F,WAAW,CAAC,UAAU,CAAC,GAAG,eAAe,CAAC;QAC1C,QAAQ,GAAG,MAAM,eAAe,CAAC;QACjC,WAAW,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC;IACrC,CAAC;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,8BAA8B;IAC5C,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;QAClD,MAAM,QAAQ,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC;QACzC,OAAO,WAAW,CAAC,UAAU,CAAC,CAAC;QAC/B,IAAI,QAAQ,YAAY,OAAO,EAAE,CAAC;YAChC,QAAQ,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;QACrD,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,UAAU,EAAE,CAAC;QACxB,CAAC;IACH,CAAC;AACH,CAAC", "sourcesContent": ["import type { DevToolsPluginClient } from './DevToolsPluginClient';\nimport { DevToolsPluginClientImplApp } from './DevToolsPluginClientImplApp';\nimport { DevToolsPluginClientImplBrowser } from './DevToolsPluginClientImplBrowser';\nimport type { ConnectionInfo, DevToolsPluginClientOptions } from './devtools.types';\nimport { getConnectionInfo } from './getConnectionInfo';\n\nconst instanceMap: Record<string, DevToolsPluginClient | Promise<DevToolsPluginClient>> = {};\n\n/**\n * Factory of DevToolsPluginClient based on sender types.\n * @hidden\n */\nexport async function createDevToolsPluginClient(\n  connectionInfo: ConnectionInfo,\n  options?: DevToolsPluginClientOptions\n): Promise<DevToolsPluginClient> {\n  let client: DevToolsPluginClient;\n  if (connectionInfo.sender === 'app') {\n    client = new DevToolsPluginClientImplApp(connectionInfo, options);\n  } else {\n    client = new DevToolsPluginClientImplBrowser(connectionInfo, options);\n  }\n  await client.initAsync();\n  return client;\n}\n\n/**\n * Public API to get the DevToolsPluginClient instance.\n */\nexport async function getDevToolsPluginClientAsync(\n  pluginName: string,\n  options?: DevToolsPluginClientOptions\n): Promise<DevToolsPluginClient> {\n  const connectionInfo = getConnectionInfo();\n\n  let instance: DevToolsPluginClient | Promise<DevToolsPluginClient> | null =\n    instanceMap[pluginName];\n  if (instance != null) {\n    if (instance instanceof Promise) {\n      return instance;\n    }\n    if (\n      instance.isConnected() === false ||\n      instance.connectionInfo.devServer !== connectionInfo.devServer\n    ) {\n      await instance.closeAsync();\n      delete instanceMap[pluginName];\n      instance = null;\n    }\n  }\n  if (instance == null) {\n    const instancePromise = createDevToolsPluginClient({ ...connectionInfo, pluginName }, options);\n    instanceMap[pluginName] = instancePromise;\n    instance = await instancePromise;\n    instanceMap[pluginName] = instance;\n  }\n  return instance;\n}\n\n/**\n * Internal testing API to cleanup all DevToolsPluginClient instances.\n */\nexport function cleanupDevToolsPluginInstances() {\n  for (const pluginName of Object.keys(instanceMap)) {\n    const instance = instanceMap[pluginName];\n    delete instanceMap[pluginName];\n    if (instance instanceof Promise) {\n      instance.then((instance) => instance.closeAsync());\n    } else {\n      instance.closeAsync();\n    }\n  }\n}\n"]}