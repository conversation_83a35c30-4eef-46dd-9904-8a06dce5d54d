{"version": 3, "file": "DevToolsPluginClient.js", "sourceRoot": "", "sources": ["../src/DevToolsPluginClient.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAChE,OAAO,EACL,sBAAsB,GAGvB,MAAM,0BAA0B,CAAC;AAMlC,OAAO,KAAK,MAAM,MAAM,UAAU,CAAC;AACnC,OAAO,EAAE,sBAAsB,EAAE,MAAM,mBAAmB,CAAC;AAW3D;;;GAGG;AACH,MAAM,OAAgB,oBAAoB;IAYtB;IACC;IAZX,SAAS,CAAyD;IAElE,MAAM,CAAC,cAAc,GAA0B,IAAI,qBAAqB,EAAE,CAAC;IAClE,OAAO,GAA0B,oBAAoB,CAAC,cAAc,CAAC;IAE5E,QAAQ,GAAG,KAAK,CAAC;IACjB,OAAO,GAAG,CAAC,CAAC;IACL,kBAAkB,GACjC,IAAI,kBAAkB,EAAE,CAAC;IAE3B,YACkB,cAA8B,EAC7B,OAAqC;QADtC,mBAAc,GAAd,cAAc,CAAgB;QAC7B,YAAO,GAAP,OAAO,CAA8B;QAEtD,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC,OAAO,IAAI,oBAAoB,CAAC,cAAc,CAAC;QAC7E,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,SAAS;QACpB,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC;YAC5B,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;QAC9C,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC;QAC3B,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,UAAU;QACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QACpE,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC;QAC3B,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC;QACzB,CAAC;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,MAAc,EAAE,MAAW;QAC5C,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,UAAU,KAAK,SAAS,CAAC,MAAM,EAAE,CAAC;YACrD,MAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;YAC/D,OAAO;QACT,CAAC;QACD,MAAM,UAAU,GAAiC;YAC/C,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,UAAU;YAC1C,MAAM;SACP,CAAC;QACF,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;QACjF,IAAI,CAAC,CAAC,UAAU,YAAY,OAAO,CAAC,EAAE,CAAC;YACrC,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YAClC,OAAO;QACT,CAAC;QACD,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;YACvB,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,MAAc,EAAE,QAA+B;QACvE,MAAM,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC;QAC1F,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjC,OAAO;YACL,MAAM,EAAE,GAAG,EAAE;gBACX,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC3C,CAAC;SACF,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACI,sBAAsB,CAAC,MAAc,EAAE,QAA+B;QAC3E,MAAM,mBAAmB,GAAG,CAAC,MAAW,EAAQ,EAAE;YAChD,QAAQ,CAAC,MAAM,CAAC,CAAC;YACjB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,mBAAmB,CAAC,CAAC;QACtD,CAAC,CAAC;QACF,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,mBAAmB,CAAC,CAAC;IACvD,CAAC;IAED;;;OAGG;IACO,oBAAoB,CAAC,MAA8B;QAC3D,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,UAAU,KAAK,SAAS,CAAC,MAAM,EAAE,CAAC;YACrD,MAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;YAC/D,OAAO;QACT,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACpF,CAAC;IAED;;;OAGG;IACO,2BAA2B,CACnC,QAAkD;QAElD,MAAM,eAAe,GAAG,CAAC,KAAmB,EAAE,EAAE;YAC9C,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACnC,yDAAyD;gBACzD,OAAO;YACT,CAAC;YAED,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACpC,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAChC,OAAO;YACT,CAAC;YACD,OAAO,IAAI,CAAC,qBAAqB,CAAC;YAClC,MAAM,MAAM,GAAG,IAA8B,CAAC;YAC9C,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,KAAK,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;gBAC9E,OAAO;YACT,CAAC;YACD,QAAQ,CAAC,MAAM,CAAC,CAAC;QACnB,CAAC,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,gBAAgB,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;QAC9D,OAAO;YACL,MAAM,EAAE,GAAG,EAAE;gBACX,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,mBAAmB,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;YACnE,CAAC;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,WAAW;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,UAAU,KAAK,SAAS,CAAC,IAAI,CAAC;IACxD,CAAC;IAED;;OAEG;IACO,YAAY;QACpB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,QAAQ,GAAG,4BAA4B,CAAC;YAC9C,MAAM,EAAE,GAAG,IAAI,sBAAsB,CAAC,QAAQ,IAAI,CAAC,cAAc,CAAC,SAAS,IAAI,QAAQ,EAAE,EAAE;gBACzF,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,mBAAmB;gBAC7C,OAAO,EAAE,CAAC,CAAU,EAAE,EAAE;oBACtB,IAAI,CAAC,YAAY,KAAK,EAAE,CAAC;wBACvB,OAAO,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;oBACzF,CAAC;yBAAM,CAAC;wBACN,OAAO,CAAC,IAAI,CAAC,iDAAiD,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;oBACrF,CAAC;gBACH,CAAC;aACF,CAAC,CAAC;YACH,EAAE,CAAC,gBAAgB,CAAC,MAAM,EAAE,GAAG,EAAE;gBAC/B,OAAO,CAAC,EAAE,CAAC,CAAC;YACd,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;gBACjC,MAAM,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC;YACH,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAsB,EAAE,EAAE;gBACtD,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAES,aAAa,GAAG,KAAK,EAAE,KAA4B,EAAE,EAAE;QAC/D,IAAI,IAA0B,CAAC;QAC/B,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YACnC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QACpB,CAAC;aAAM,IAAI,KAAK,CAAC,IAAI,YAAY,WAAW,EAAE,CAAC;YAC7C,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QACpB,CAAC;aAAM,IAAI,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1C,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,MAAqB,CAAC;QAC1C,CAAC;aAAM,IAAI,KAAK,CAAC,IAAI,YAAY,IAAI,EAAE,CAAC;YACtC,IAAI,GAAG,MAAM,sBAAsB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;YACnE,OAAO;QACT,CAAC;QACD,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC9E,gHAAgH;QAChH,IAAI,IAAI,EAAE,qBAAqB,KAAK,IAAI,EAAE,CAAC;YACzC,OAAO;QACT,CAAC;QACD,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,CAAC,UAAU,KAAK,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;YACtF,OAAO;QACT,CAAC;QAED,MAAM,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAC7D,IAAI,kBAAkB,EAAE,CAAC;YACvB,KAAK,MAAM,QAAQ,IAAI,kBAAkB,EAAE,CAAC;gBAC1C,QAAQ,CAAC,OAAO,CAAC,CAAC;YACpB,CAAC;QACH,CAAC;IACH,CAAC,CAAC;IAEF;;;OAGG;IACI,wBAAwB;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC", "sourcesContent": ["import { MessageFramePacker } from './MessageFramePacker';\nimport { WebSocketBackingStore } from './WebSocketBackingStore';\nimport {\n  WebSocketWithReconnect,\n  type WebSocketCloseEvent,\n  type WebSocketMessageEvent,\n} from './WebSocketWithReconnect';\nimport type {\n  ConnectionInfo,\n  DevToolsPluginClientOptions,\n  HandshakeMessageParams,\n} from './devtools.types';\nimport * as logger from './logger';\nimport { blobToArrayBufferAsync } from './utils/blobUtils';\n\ninterface MessageFramePackerMessageKey {\n  pluginName: string;\n  method: string;\n}\n\nexport interface EventSubscription {\n  remove(): void;\n}\n\n/**\n * This client is for the Expo DevTools Plugins to communicate between the app and the DevTools webpage hosted in a browser.\n * All the code should be both compatible with browsers and React Native.\n */\nexport abstract class DevToolsPluginClient {\n  private listeners: Record<string, undefined | Set<(params: any) => void>>;\n\n  private static defaultWSStore: WebSocketBackingStore = new WebSocketBackingStore();\n  private readonly wsStore: WebSocketBackingStore = DevToolsPluginClient.defaultWSStore;\n\n  protected isClosed = false;\n  protected retries = 0;\n  private readonly messageFramePacker: MessageFramePacker<MessageFramePackerMessageKey> =\n    new MessageFramePacker();\n\n  public constructor(\n    public readonly connectionInfo: ConnectionInfo,\n    private readonly options?: DevToolsPluginClientOptions\n  ) {\n    this.wsStore = connectionInfo.wsStore || DevToolsPluginClient.defaultWSStore;\n    this.listeners = Object.create(null);\n  }\n\n  /**\n   * Initialize the connection.\n   * @hidden\n   */\n  public async initAsync(): Promise<void> {\n    if (this.wsStore.ws == null) {\n      this.wsStore.ws = await this.connectAsync();\n    }\n    this.wsStore.refCount += 1;\n    this.wsStore.ws.addEventListener('message', this.handleMessage);\n  }\n\n  /**\n   * Close the connection.\n   */\n  public async closeAsync(): Promise<void> {\n    this.isClosed = true;\n    this.wsStore.ws?.removeEventListener('message', this.handleMessage);\n    this.wsStore.refCount -= 1;\n    if (this.wsStore.refCount < 1) {\n      this.wsStore.ws?.close();\n      this.wsStore.ws = null;\n    }\n    this.listeners = Object.create(null);\n  }\n\n  /**\n   * Send a message to the other end of DevTools.\n   * @param method A method name.\n   * @param params any extra payload.\n   */\n  public sendMessage(method: string, params: any) {\n    if (this.wsStore.ws?.readyState === WebSocket.CLOSED) {\n      logger.warn('Unable to send message in a disconnected state.');\n      return;\n    }\n    const messageKey: MessageFramePackerMessageKey = {\n      pluginName: this.connectionInfo.pluginName,\n      method,\n    };\n    const packedData = this.messageFramePacker.pack({ messageKey, payload: params });\n    if (!(packedData instanceof Promise)) {\n      this.wsStore.ws?.send(packedData);\n      return;\n    }\n    packedData.then((data) => {\n      this.wsStore.ws?.send(data);\n    });\n  }\n\n  /**\n   * Subscribe to a message from the other end of DevTools.\n   * @param method Subscribe to a message with a method name.\n   * @param listener Listener to be called when a message is received.\n   */\n  public addMessageListener(method: string, listener: (params: any) => void): EventSubscription {\n    const listenersForMethod = this.listeners[method] || (this.listeners[method] = new Set());\n    listenersForMethod.add(listener);\n    return {\n      remove: () => {\n        this.listeners[method]?.delete(listener);\n      },\n    };\n  }\n\n  /**\n   * Subscribe to a message from the other end of DevTools just once.\n   * @param method Subscribe to a message with a method name.\n   * @param listener Listener to be called when a message is received.\n   */\n  public addMessageListenerOnce(method: string, listener: (params: any) => void): void {\n    const wrappedListenerOnce = (params: any): void => {\n      listener(params);\n      this.listeners[method]?.delete(wrappedListenerOnce);\n    };\n    this.addMessageListener(method, wrappedListenerOnce);\n  }\n\n  /**\n   * Internal handshake message sender.\n   * @hidden\n   */\n  protected sendHandshakeMessage(params: HandshakeMessageParams) {\n    if (this.wsStore.ws?.readyState === WebSocket.CLOSED) {\n      logger.warn('Unable to send message in a disconnected state.');\n      return;\n    }\n    this.wsStore.ws?.send(JSON.stringify({ ...params, __isHandshakeMessages: true }));\n  }\n\n  /**\n   * Internal handshake message listener.\n   * @hidden\n   */\n  protected addHandskakeMessageListener(\n    listener: (params: HandshakeMessageParams) => void\n  ): EventSubscription {\n    const messageListener = (event: MessageEvent) => {\n      if (typeof event.data !== 'string') {\n        // binary data is not coming from the handshake messages.\n        return;\n      }\n\n      const data = JSON.parse(event.data);\n      if (!data.__isHandshakeMessages) {\n        return;\n      }\n      delete data.__isHandshakeMessages;\n      const params = data as HandshakeMessageParams;\n      if (params.pluginName && params.pluginName !== this.connectionInfo.pluginName) {\n        return;\n      }\n      listener(params);\n    };\n\n    this.wsStore.ws?.addEventListener('message', messageListener);\n    return {\n      remove: () => {\n        this.wsStore.ws?.removeEventListener('message', messageListener);\n      },\n    };\n  }\n\n  /**\n   * Returns whether the client is connected to the server.\n   */\n  public isConnected(): boolean {\n    return this.wsStore.ws?.readyState === WebSocket.OPEN;\n  }\n\n  /**\n   * The method to create the WebSocket connection.\n   */\n  protected connectAsync(): Promise<WebSocket> {\n    return new Promise((resolve, reject) => {\n      const endpoint = 'expo-dev-plugins/broadcast';\n      const ws = new WebSocketWithReconnect(`ws://${this.connectionInfo.devServer}/${endpoint}`, {\n        binaryType: this.options?.websocketBinaryType,\n        onError: (e: unknown) => {\n          if (e instanceof Error) {\n            console.warn(`Error happened from the WebSocket connection: ${e.message}\\n${e.stack}`);\n          } else {\n            console.warn(`Error happened from the WebSocket connection: ${JSON.stringify(e)}`);\n          }\n        },\n      });\n      ws.addEventListener('open', () => {\n        resolve(ws);\n      });\n      ws.addEventListener('error', (e) => {\n        reject(e);\n      });\n      ws.addEventListener('close', (e: WebSocketCloseEvent) => {\n        logger.info('WebSocket closed', e.code, e.reason);\n      });\n    });\n  }\n\n  protected handleMessage = async (event: WebSocketMessageEvent) => {\n    let data: ArrayBuffer | string;\n    if (typeof event.data === 'string') {\n      data = event.data;\n    } else if (event.data instanceof ArrayBuffer) {\n      data = event.data;\n    } else if (ArrayBuffer.isView(event.data)) {\n      data = event.data.buffer as ArrayBuffer;\n    } else if (event.data instanceof Blob) {\n      data = await blobToArrayBufferAsync(event.data);\n    } else {\n      logger.warn('Unsupported received data type in handleMessageImpl');\n      return;\n    }\n    const { messageKey, payload, ...rest } = this.messageFramePacker.unpack(data);\n    // @ts-expect-error: `__isHandshakeMessages` is a private field that is not part of the MessageFramePacker type.\n    if (rest?.__isHandshakeMessages === true) {\n      return;\n    }\n    if (messageKey.pluginName && messageKey.pluginName !== this.connectionInfo.pluginName) {\n      return;\n    }\n\n    const listenersForMethod = this.listeners[messageKey.method];\n    if (listenersForMethod) {\n      for (const listener of listenersForMethod) {\n        listener(payload);\n      }\n    }\n  };\n\n  /**\n   * Get the WebSocket backing store. Exposed for testing.\n   * @hidden\n   */\n  public getWebSocketBackingStore(): WebSocketBackingStore {\n    return this.wsStore;\n  }\n}\n"]}