{"version": 3, "file": "getConnectionInfo.native.js", "sourceRoot": "", "sources": ["../src/getConnectionInfo.native.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAE,gBAAgB,EAAE,MAAM,mBAAmB,CAAC;AAGrD,MAAM,UAAU,iBAAiB;IAC/B,MAAM,YAAY,GAAG,OAAO,CAAC,mDAAmD,CAAC,CAAC,OAAO,CAAC;IAC1F,MAAM,SAAS,GAAG,YAAY,EAAE;SAC7B,GAAG,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;SAC/B,OAAO,CAAC,MAAM,EAAE,EAAE,CAAW,CAAC;IACjC,OAAO;QACL,eAAe,EAAE,gBAAgB;QACjC,MAAM,EAAE,KAAK;QACb,SAAS;KACV,CAAC;AACJ,CAAC", "sourcesContent": ["/**\n * Get the dev server address.\n */\n\nimport { PROTOCOL_VERSION } from './ProtocolVersion';\nimport type { ConnectionInfo } from './devtools.types';\n\nexport function getConnectionInfo(): Omit<ConnectionInfo, 'pluginName'> {\n  const getDevServer = require('react-native/Libraries/Core/Devtools/getDevServer').default;\n  const devServer = getDevServer()\n    .url.replace(/^https?:\\/\\//, '')\n    .replace(/\\/?$/, '') as string;\n  return {\n    protocolVersion: PROTOCOL_VERSION,\n    sender: 'app',\n    devServer,\n  };\n}\n"]}