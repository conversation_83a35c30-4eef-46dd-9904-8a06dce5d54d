{"name": "@expo/devtools", "version": "0.1.7", "description": "DevTools plugin helpers for Expo", "main": "build/index.js", "types": "build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "typecheck": "expo-module typecheck"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/@expo/devtools"}, "keywords": ["expo", "react-native", "react", "devtools", "plugins", "dev-plugins", "cli-extensions"], "license": "MIT", "bugs": {"url": "https://github.com/expo/expo/issues"}, "homepage": "https://github.com/expo/expo/tree/main/packages/@expo/devtools#readme", "files": ["build"], "dependencies": {"chalk": "^4.1.2"}, "devDependencies": {"expo-module-scripts": "^5.0.7", "ws": "^8.18.0"}, "peerDependencies": {"react": "*", "react-native": "*"}, "peerDependenciesMeta": {"react": {"optional": true}, "react-native": {"optional": true}}, "gitHead": "088e79428be97cf3ee11fc93e0e5a1fc1c8bea1e"}