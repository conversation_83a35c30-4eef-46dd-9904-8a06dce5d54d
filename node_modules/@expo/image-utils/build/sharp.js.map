{"version": 3, "file": "sharp.js", "sourceRoot": "", "sources": ["../src/sharp.ts"], "names": [], "mappings": ";;;;;AAcA,8CAsBC;AAMD,4CAYC;AAED,gCAwBC;AA4FD,wDAiBC;AA7LD,oEAA2C;AAC3C,oDAA4B;AAC5B,kDAA0B;AAC1B,gDAAwB;AACxB,gEAAuC;AACvC,oEAA2C;AAC3C,oDAA4B;AAE5B,+BAA4B;AAG5B,MAAM,kBAAkB,GAAG,2CAA2C,CAAC;AACvE,MAAM,sBAAsB,GAAG,QAAQ,CAAC;AAEjC,KAAK,UAAU,iBAAiB,CAAC,MAAc,EAAE,KAAe;IACrE,MAAM,KAAK,GAAG,MAAM,sBAAsB,EAAE,CAAC;IAC7C,IAAA,gBAAM,EAAC,KAAK,EAAE,+CAA+C,CAAC,CAAC;IAE/D,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;IAChD,8BAA8B;IAC9B,MAAM,cAAc,GAAG,MAAM,OAAO,CAAC,GAAG,CACtC,KAAK,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;QACtB,MAAM,OAAO,GACX,QAAQ,CAAC,OAAO,IAAI,IAAI;YACtB,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,QAAQ,CAAC,OAAO;YAC5E,CAAC,CAAC,IAAI,CAAC;QAEX,OAAO,KAAK,CAAC,MAAM,EAAE;YACnB,OAAO,EAAE,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;SAC1D,CAAC;aACC,MAAM,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,CAAC;aAC3E,QAAQ,EAAE,CAAC;IAChB,CAAC,CAAC,CACH,CAAC;IAEF,OAAO,cAAc,CAAC;AACxB,CAAC;AAED;;;GAGG;AACI,KAAK,UAAU,gBAAgB;IACpC,IAAI,SAAG,CAAC,yBAAyB,EAAE,CAAC;QAClC,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,CAAC;QACH,wBAAwB;QACxB,MAAM,sBAAsB,EAAE,CAAC;QAC/B,4DAA4D;QAC5D,OAAO,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,cAAc,CAAC;IACzC,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAEM,KAAK,UAAU,UAAU,CAC9B,OAA2B,EAC3B,WAAkC,EAAE;IAEpC,MAAM,GAAG,GAAG,MAAM,iBAAiB,EAAE,CAAC;IACtC,IAAI,CAAC;QACH,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAA,qBAAU,EAAC,GAAG,EAAE;YACvC,GAAG,UAAU,CAAC,OAAO,CAAC;YACtB,GAAG,iBAAiB,CAAC,QAAQ,CAAC;SAC/B,CAAC,CAAC;QACH,MAAM,eAAe,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClD,OAAO,eAAe,CAAC;IACzB,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CACb,8CAA8C;gBAC5C,KAAK,CAAC,OAAO;gBACb,YAAY;gBACZ,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAC/C,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;AACH,CAAC;AAED,SAAS,UAAU,CAAC,OAAgB;IAClC,MAAM,IAAI,GAAG,EAAE,CAAC;IAChB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;QACnD,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,EAAE,CAAC;YACrC,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC/B,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC;YACxB,CAAC;iBAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACrC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACzC,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,EAAE,KAAK,CAAC,CAAC;YAC/B,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,iBAAiB,CAAC,QAA+B;IACxD,MAAM,IAAI,GAAa,EAAE,CAAC;IAC1B,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;QAC/B,IAAI,OAAO,CAAC,SAAS,KAAK,QAAQ,EAAE,CAAC;YACnC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,YAAY,EAAE,GAAG,OAAO,CAAC;YACtD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,OAAO,EAAE,EAAE,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC;QACrE,CAAC;aAAM,CAAC;YACN,MAAM,EAAE,SAAS,EAAE,GAAG,YAAY,EAAE,GAAG,OAAO,CAAC;YAC/C,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC;QACpD,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClB,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,IAAI,SAAS,GAAkB,IAAI,CAAC;AACpC,IAAI,cAAc,GAAkC,IAAI,CAAC;AAEzD,KAAK,UAAU,iBAAiB;IAC9B,IAAI,SAAS;QAAE,OAAO,SAAS,CAAC;IAEhC,IAAI,CAAC;QACH,MAAM,mBAAmB,GACvB,wBAAa,CAAC,MAAM,CAAC,wBAAwB,CAAC;YAC9C,OAAO,CAAC,OAAO,CAAC,wBAAwB,EAAE;gBACxC,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,SAAS;aACvD,CAAC,CAAC;QAEL,MAAM,eAAe,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;QACrD,MAAM,aAAa,GAAG,mBAAmB;YACvC,CAAC,CAAC,OAAO,CAAC,IAAA,sBAAW,EAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;YACpD,CAAC,CAAC,IAAI,CAAC;QAET,IACE,mBAAmB;YACnB,gBAAM,CAAC,SAAS,CAAC,eAAe,CAAC,OAAO,EAAE,sBAAsB,CAAC;YACjE,OAAO,eAAe,CAAC,GAAG,CAAC,KAAK,KAAK,QAAQ;YAC7C,OAAO,cAAc,EAAE,QAAQ,EAAE,IAAI,KAAK,QAAQ,EAClD,CAAC;YACD,SAAS,GAAG,cAAI,CAAC,IAAI,CAAC,cAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACpF,cAAc,GAAG,aAAa,CAAC;YAE/B,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,SAAS,GAAG,IAAI,CAAC;QACjB,cAAc,GAAG,IAAI,CAAC;QAEtB,4FAA4F;QAC5F,IAAI,SAAG,CAAC,sBAAsB,EAAE,CAAC;YAC/B,OAAO,CAAC,IAAI,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED,IAAI,mBAAmB,CAAC;IACxB,IAAI,CAAC;QACH,mBAAmB,GAAG,CAAC,MAAM,IAAA,qBAAU,EAAC,OAAO,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;IAC5F,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,IAAI,CAAC,gBAAM,CAAC,SAAS,CAAC,mBAAmB,EAAE,sBAAsB,CAAC,EAAE,CAAC;QACnE,0BAA0B,CAAC,sBAAsB,EAAE,mBAAmB,CAAC,CAAC;QACxE,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,0CAA0C;IAC1C,SAAS,GAAG,OAAO,CAAC;IACpB,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;;GAGG;AACI,KAAK,UAAU,sBAAsB;IAC1C,IAAI,SAAG,CAAC,yBAAyB,EAAE,CAAC;QAClC,MAAM,IAAI,KAAK,CACb,gJAAgJ,CACjJ,CAAC;IACJ,CAAC;IAED,6BAA6B;IAC7B,IAAI,cAAc;QAAE,OAAO,cAAc,CAAC;IAC1C,uFAAuF;IACvF,MAAM,iBAAiB,EAAE,CAAC;IAE1B,IAAI,CAAC,cAAc,EAAE,CAAC;QACpB,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAC;IAChG,CAAC;IAED,OAAO,cAAc,CAAC;AACxB,CAAC;AAED,IAAI,2BAA2B,GAAG,KAAK,CAAC;AAExC,SAAS,0BAA0B,CAAC,kBAA0B,EAAE,mBAA2B;IACzF,IAAI,2BAA2B,EAAE,CAAC;QAChC,OAAO;IACT,CAAC;IACD,OAAO,CAAC,IAAI,CACV;QACE,eAAK,CAAC,MAAM,CACV,0BAA0B,kBAAkB,yCAAyC,mBAAmB,IAAI,CAC7G;QACD,eAAK,CAAC,MAAM,CAAC,GAAG,CACd,qEAAqE,kBAAkB,KAAK,CAC7F;QACD,eAAK,CAAC,MAAM,CAAC,GAAG,CAAC,gEAAgE,CAAC;KACnF,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;IACF,2BAA2B,GAAG,IAAI,CAAC;AACrC,CAAC"}